/* Abbreviated Text Expander Styles */

.abbreviated-text-expander-view {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--bg-color);
    color: var(--text-color);
    font-family: var(--font-family);
}

.abbreviated-text-expander-layout {
    display: flex;
    flex: 1;
    gap: 20px;
    padding: 20px;
    overflow: hidden;
}

.abbreviated-text-expander-controls-panel {
    width: 650px;
    background-color: var(--frame-bg-color);
    border-radius: 8px;
    padding: 15px;
    overflow-y: auto;
    border: 1px solid var(--accent-color);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    align-items: start;
}

.abbreviated-text-expander-main-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow: hidden;
}

.abbreviated-text-expander-control-group {
    margin-bottom: 8px;
}

.button-with-dropdown {
    position: relative;
    background-color: var(--bg-color);
    border-radius: 4px;
    padding: 12px 15px;
    border: 1px solid rgba(176, 158, 128, 0.2);
    transition: all 0.3s ease;
    min-width: 250px;
}

.button-with-dropdown:hover {
    border-color: var(--accent-color);
    box-shadow: 0 2px 8px rgba(176, 158, 128, 0.1);
}

.button-with-dropdown button {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    padding: 10px 12px;
    font-size: 14px;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    min-width: 200px;
    min-height: 44px;
}

.button-with-dropdown button span:first-child {
    flex: 1;
    text-align: left;
    white-space: normal;
    word-wrap: break-word;
    overflow: visible;
    margin-right: 8px;
    line-height: 1.2;
}

.dropdown-toggle {
    background-color: rgba(176, 158, 128, 0.2);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    color: var(--accent-color);
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.dropdown-toggle:hover {
    background-color: var(--accent-color);
    color: white;
}

.button-description {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
    background-color: rgba(176, 158, 128, 0.05);
    border-radius: 4px;
    margin-top: 0;
}

.button-with-dropdown.expanded .button-description {
    max-height: 300px;
    padding: 10px;
    margin-top: 8px;
    border: 1px solid rgba(176, 158, 128, 0.2);
}

.button-with-dropdown.expanded .dropdown-toggle {
    transform: rotate(180deg);
    background-color: var(--accent-color);
    color: white;
}

.button-description p {
    margin: 0 0 6px 0;
    font-size: 12px;
    line-height: 1.3;
    color: var(--text-color);
}

.button-description ul {
    margin: 6px 0;
    padding-left: 16px;
}

.button-description li {
    font-size: 11px;
    line-height: 1.2;
    color: var(--text-color);
    margin-bottom: 3px;
}

.button-description em {
    font-size: 12px;
    opacity: 0.8;
    font-style: italic;
}

/* Run All Section Styling */
.run-all-section {
    grid-column: 1 / -1;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--accent-color);
}

.master-button button {
    background: linear-gradient(135deg, var(--accent-color), #c4a574);
    color: white;
    font-weight: 600;
    font-size: 15px;
    padding: 12px 16px;
    border: none;
    box-shadow: 0 3px 10px rgba(176, 158, 128, 0.3);
    transition: all 0.3s ease;
}

.master-button button:hover {
    background: linear-gradient(135deg, #c4a574, var(--accent-color));
    box-shadow: 0 5px 15px rgba(176, 158, 128, 0.4);
    transform: translateY(-1px);
}

.master-button button:disabled {
    background: #ccc;
    color: #666;
    box-shadow: none;
    transform: none;
}

.master-button .dropdown-toggle {
    background-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.master-button .dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

.master-button.expanded .dropdown-toggle {
    background-color: rgba(255, 255, 255, 0.5);
}

/* Progress Indicator */
.progress-indicator {
    margin-top: 12px;
    padding: 12px;
    background-color: rgba(176, 158, 128, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(176, 158, 128, 0.3);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: rgba(176, 158, 128, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-color), #c4a574);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 12px;
    line-height: 1.3;
    color: var(--text-color);
    margin-bottom: 8px;
}

.progress-text strong {
    color: var(--accent-color);
    font-weight: 600;
}

.control-button.small {
    padding: 6px 12px;
    font-size: 11px;
    margin-top: 4px;
}

/* Ordered list styling for run all description */
.button-description ol {
    margin: 8px 0;
    padding-left: 18px;
}

.button-description ol li {
    font-size: 11px;
    line-height: 1.2;
    color: var(--text-color);
    margin-bottom: 3px;
}

.abbreviated-text-expander-control-group h3 {
    color: var(--accent-color);
    margin-bottom: 15px;
    font-size: 1.1em;
    font-weight: 600;
    border-bottom: 1px solid var(--accent-color);
    padding-bottom: 5px;
}

.abbreviated-text-expander-document-wrapper {
    flex: 1;
    background-color: var(--frame-bg-color);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--accent-color);
    overflow: visible;
    display: flex;
    flex-direction: column;
}

.document-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--accent-color);
}

.document-header h3 {
    color: var(--accent-color);
    font-size: 1.1em;
    font-weight: 600;
    margin: 0;
}

.document-stats {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: var(--text-color);
    opacity: 0.7;
}

.document-stats span {
    background-color: var(--bg-color);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid var(--accent-color);
}

.document-container {
    flex: 1;
    overflow: visible;
    border: 1px solid var(--accent-color);
    border-radius: 4px;
    background-color: var(--bg-color);
    position: relative;
    min-height: 400px;
    max-height: 500px;
}

.document-editor {
    width: 100%;
    height: 100%;
    max-height: 450px;
    padding: 20px;
    border: none;
    background-color: transparent;
    color: var(--text-color);
    font-family: 'Georgia', 'Times New Roman', serif;
    font-size: 15px;
    line-height: 1.8;
    resize: none;
    outline: none;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;

    /* Ensure text selection works properly */
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;

    /* Improve text rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.document-editor:empty::before {
    content: attr(data-placeholder);
    color: var(--text-color);
    opacity: 0.5;
    font-style: italic;
    pointer-events: none;
}

.document-editor:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: -2px;
}

/* Enhanced selection highlighting for contenteditable */
.document-editor::selection {
    background-color: rgba(176, 158, 128, 0.8) !important;
    color: var(--bg-color) !important;
    text-shadow: none !important;
}

.document-editor::-moz-selection {
    background-color: rgba(176, 158, 128, 0.8) !important;
    color: var(--bg-color) !important;
    text-shadow: none !important;
}

/* Force selection visibility in contenteditable */
.document-editor {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;

    /* Force selection highlighting */
    -webkit-highlight: rgba(176, 158, 128, 0.8);
    -moz-highlight: rgba(176, 158, 128, 0.8);
}

/* Custom selection highlighting class */
.document-editor .custom-selection {
    background-color: rgba(176, 158, 128, 0.6) !important;
    color: var(--bg-color) !important;
    border-radius: 2px;
    padding: 0 1px;
    box-shadow: 0 0 0 1px rgba(176, 158, 128, 0.3);
}

/* Ensure text nodes show selection */
.document-editor * {
    -webkit-user-select: inherit;
    -moz-user-select: inherit;
    -ms-user-select: inherit;
    user-select: inherit;
}

.document-editor *::selection {
    background-color: rgba(176, 158, 128, 0.8) !important;
    color: var(--bg-color) !important;
    text-shadow: none !important;
}

.document-editor *::-moz-selection {
    background-color: rgba(176, 158, 128, 0.8) !important;
    color: var(--bg-color) !important;
    text-shadow: none !important;
}

/* Ensure selection is visible during drag */
.document-editor {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* Custom selection highlighting for better visibility */
.document-editor.selecting {
    cursor: text;
    background-color: rgba(176, 158, 128, 0.02);

    /* Force selection visibility during drag */
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    user-select: text !important;
}

.document-editor.focused {
    outline: 2px solid var(--accent-color);
    outline-offset: -2px;
    background-color: rgba(176, 158, 128, 0.01);
}

/* Enhanced selection visibility when active */
.document-editor[style*="--selection-active"] {
    background-color: rgba(176, 158, 128, 0.03);
}

/* Force selection highlighting in contenteditable */
.document-editor:focus::selection,
.document-editor:focus *::selection {
    background-color: rgba(176, 158, 128, 0.8) !important;
    color: var(--bg-color) !important;
}

.document-editor:focus::-moz-selection,
.document-editor:focus *::-moz-selection {
    background-color: rgba(176, 158, 128, 0.8) !important;
    color: var(--bg-color) !important;
}

.document-editor .text-highlight {
    background-color: rgba(176, 158, 128, 0.3);
    border-radius: 2px;
    padding: 1px 2px;
}

/* Improve selection visibility on different backgrounds - already defined above */

/* Ensure text cursor is visible */
.document-editor:focus {
    caret-color: var(--accent-color);
}

/* Better visual feedback during text selection */
.document-editor:active {
    background-color: rgba(176, 158, 128, 0.02);
}

/* Force selection highlighting - aggressive approach */
.document-editor {
    /* Webkit specific */
    -webkit-user-select: text;
    -webkit-touch-callout: text;
    -webkit-tap-highlight-color: rgba(176, 158, 128, 0.8);

    /* Firefox specific */
    -moz-user-select: text;

    /* IE/Edge specific */
    -ms-user-select: text;

    /* Standard */
    user-select: text;
}

/* Ensure selection works on all text nodes */
.document-editor,
.document-editor * {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* Override any potential interference */
.document-editor:not(.no-select) {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    user-select: text !important;
}

.document-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(25, 37, 55, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--frame-bg-color);
    border-top: 3px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.document-toolbar {
    display: flex;
    gap: 8px;
    padding: 12px 0;
    border-top: 1px solid var(--accent-color);
    margin-top: 15px;
    background-color: var(--frame-bg-color);
    border-radius: 4px;
    padding: 12px;
    flex-shrink: 0;
}

.document-toolbar .control-button.small {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 80px;
    background-color: var(--bg-color);
    border: 1px solid var(--accent-color);
    color: var(--text-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.document-toolbar .control-button.small:hover:not(:disabled) {
    background-color: var(--accent-color);
    color: var(--bg-color);
}

.document-toolbar .control-button.small:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}



.selection-stats span {
    background-color: var(--bg-color);
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid var(--accent-color);
}

.selection-actions {
    display: flex;
    gap: 8px;
    padding-top: 10px;
    border-top: 1px solid var(--accent-color);
    margin-top: 10px;
}

.selection-actions .control-button.small {
    padding: 6px 12px;
    font-size: 12px;
    min-width: auto;
    flex: 1;
}

.config-item {
    margin-bottom: 15px;
}

.config-item label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-color);
    font-weight: 500;
}

.config-item input[type="password"],
.config-item input[type="text"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--accent-color);
    border-radius: 4px;
    background-color: var(--bg-color);
    color: var(--text-color);
    font-family: var(--font-family);
    font-size: 14px;
}

.config-item input[type="password"]:focus,
.config-item input[type="text"]:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(176, 158, 128, 0.2);
}

.expansion-info {
    background-color: rgba(176, 158, 128, 0.05);
    border: 1px solid rgba(176, 158, 128, 0.3);
    border-radius: 4px;
    padding: 12px;
    margin-top: 8px;
}

.expansion-info p {
    margin: 0;
    font-size: 13px;
    line-height: 1.5;
    color: var(--text-color);
    opacity: 0.8;
}

.expansion-preview {
    background-color: var(--bg-color);
    border: 1px solid var(--accent-color);
    border-radius: 4px;
    padding: 12px;
    margin-top: 15px;
}

.expansion-preview h4 {
    color: var(--accent-color);
    margin: 0 0 8px 0;
    font-size: 13px;
    font-weight: 600;
}

.instructions-preview {
    font-size: 12px;
    line-height: 1.4;
    color: var(--text-color);
    opacity: 0.8;
    font-style: italic;
}

.config-item textarea {
    width: 100%;
    min-height: 80px;
    padding: 8px 12px;
    border: 1px solid var(--accent-color);
    border-radius: 4px;
    background-color: var(--bg-color);
    color: var(--text-color);
    font-family: var(--font-family);
    font-size: 13px;
    line-height: 1.4;
    resize: vertical;
}

.config-item textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(176, 158, 128, 0.2);
}

.config-item select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--accent-color);
    border-radius: 4px;
    background-color: var(--bg-color);
    color: var(--text-color);
    font-family: var(--font-family);
    font-size: 14px;
}

.config-item select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(176, 158, 128, 0.2);
}

.model-info {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: rgba(176, 158, 128, 0.1);
    border-radius: 4px;
    border-left: 3px solid var(--accent-color);
}

.model-info p {
    margin: 0;
    font-size: 12px;
    line-height: 1.4;
    color: var(--text-color);
    opacity: 0.9;
}

.api-key-help {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: rgba(176, 158, 128, 0.05);
    border-radius: 4px;
    border: 1px solid rgba(176, 158, 128, 0.2);
}

.api-key-help p {
    margin: 4px 0;
    font-size: 12px;
    line-height: 1.4;
    color: var(--text-color);
}

.api-key-help a {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
}

.api-key-help a:hover {
    text-decoration: underline;
}

.api-key-help small {
    font-size: 11px;
    opacity: 0.8;
}

.scripture-info {
    background-color: rgba(176, 158, 128, 0.05);
    border: 1px solid rgba(176, 158, 128, 0.3);
    border-radius: 4px;
    padding: 12px;
    margin-top: 8px;
}

.scripture-info p {
    margin: 0 0 10px 0;
    font-size: 13px;
    line-height: 1.5;
    color: var(--text-color);
}

.scripture-info ul {
    margin: 10px 0;
    padding-left: 20px;
}

.scripture-info li {
    font-size: 12px;
    line-height: 1.4;
    color: var(--text-color);
    margin-bottom: 5px;
}

.scripture-info em {
    font-size: 12px;
    opacity: 0.8;
}

.quote-info {
    background-color: rgba(176, 158, 128, 0.05);
    border: 1px solid rgba(176, 158, 128, 0.3);
    border-radius: 4px;
    padding: 12px;
    margin-top: 8px;
}

.quote-info p {
    margin: 0 0 10px 0;
    font-size: 13px;
    line-height: 1.5;
    color: var(--text-color);
}

.quote-info ul {
    margin: 10px 0;
    padding-left: 20px;
}

.quote-info li {
    font-size: 12px;
    line-height: 1.4;
    color: var(--text-color);
    margin-bottom: 5px;
}

.quote-info em {
    font-size: 12px;
    opacity: 0.8;
}

.abbreviation-info {
    background-color: rgba(176, 158, 128, 0.05);
    border: 1px solid rgba(176, 158, 128, 0.3);
    border-radius: 4px;
    padding: 12px;
    margin-top: 8px;
}

.abbreviation-info p {
    margin: 0 0 10px 0;
    font-size: 13px;
    line-height: 1.5;
    color: var(--text-color);
}

.abbreviation-info ul {
    margin: 10px 0;
    padding-left: 20px;
}

.abbreviation-info li {
    font-size: 12px;
    line-height: 1.4;
    color: var(--text-color);
    margin-bottom: 5px;
}

.abbreviation-info em {
    font-size: 12px;
    opacity: 0.8;
}

.papal-saints-info {
    background-color: rgba(176, 158, 128, 0.05);
    border: 1px solid rgba(176, 158, 128, 0.3);
    border-radius: 4px;
    padding: 12px;
    margin-top: 8px;
}

.papal-saints-info p {
    margin: 0 0 10px 0;
    font-size: 13px;
    line-height: 1.5;
    color: var(--text-color);
}

.papal-saints-info ul {
    margin: 10px 0;
    padding-left: 20px;
}

.papal-saints-info li {
    font-size: 12px;
    line-height: 1.4;
    color: var(--text-color);
    margin-bottom: 5px;
}

.papal-saints-info em {
    font-size: 12px;
    opacity: 0.8;
}

.footnotes-info {
    background-color: rgba(176, 158, 128, 0.05);
    border: 1px solid rgba(176, 158, 128, 0.3);
    border-radius: 4px;
    padding: 12px;
    margin-top: 8px;
}

.footnotes-info p {
    margin: 0 0 10px 0;
    font-size: 13px;
    line-height: 1.5;
    color: var(--text-color);
}

.footnotes-info ul {
    margin: 10px 0;
    padding-left: 20px;
}

.footnotes-info li {
    font-size: 12px;
    line-height: 1.4;
    color: var(--text-color);
    margin-bottom: 5px;
}

.footnotes-info em {
    font-size: 12px;
    opacity: 0.8;
}

/* Text Expansion Preview Modal */
.expansion-preview-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.original-text-section,
.expanded-text-section {
    background-color: var(--frame-bg-color);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid var(--accent-color);
}

.original-text-section h4,
.expanded-text-section h4 {
    color: var(--accent-color);
    margin-bottom: 10px;
    font-size: 1em;
    font-weight: 600;
}

.text-display {
    background-color: var(--bg-color);
    border: 1px solid var(--accent-color);
    border-radius: 4px;
    padding: 12px;
    min-height: 80px;
    max-height: 200px;
    overflow-y: auto;
    font-family: var(--font-family);
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-color);
}

.text-display.original {
    background-color: rgba(176, 158, 128, 0.1);
}

.text-display.expanded {
    background-color: rgba(176, 158, 128, 0.05);
}

.text-display.expanded[contenteditable="true"]:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: -2px;
}

.preview-controls {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid var(--accent-color);
}

/* Button Styles */
.control-button.secondary {
    background-color: var(--frame-bg-color);
    color: var(--text-color);
    border: 1px solid var(--accent-color);
}

.control-button.secondary:hover {
    background-color: var(--accent-color);
    color: var(--bg-color);
}

.control-button.secondary:disabled {
    background-color: var(--frame-bg-color);
    color: var(--text-color);
    opacity: 0.5;
    cursor: not-allowed;
}

/* Status Bar */
.abbreviated-text-expander-view .status-bar {
    padding: 10px 20px;
    background-color: var(--frame-bg-color);
    border-top: 1px solid var(--accent-color);
    font-size: 14px;
    transition: all 0.3s ease;
}

/* Status Bar Message Types */
.abbreviated-text-expander-view .status-bar.success {
    background-color: #d4edda;
    color: #155724;
    border-top-color: #28a745;
}

.abbreviated-text-expander-view .status-bar.error {
    background-color: #f8d7da;
    color: #721c24;
    border-top-color: #dc3545;
}

.abbreviated-text-expander-view .status-bar.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border-top-color: #17a2b8;
}

.abbreviated-text-expander-view .status-bar.warning {
    background-color: #fff3cd;
    color: #856404;
    border-top-color: #ffc107;
}

/* Medium screen responsiveness */
@media (max-width: 1400px) and (min-width: 1201px) {
    .abbreviated-text-expander-controls-panel {
        width: 520px;
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 15px;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .abbreviated-text-expander-layout {
        flex-direction: column;
        gap: 15px;
    }
    
    .abbreviated-text-expander-controls-panel {
        width: 100%;
        max-height: 400px;
        grid-template-columns: 1fr;
        gap: 10px;
        padding: 12px;
    }

    .button-with-dropdown {
        min-width: 200px;
    }

    .run-all-section {
        margin-bottom: 12px;
        padding-bottom: 12px;
    }

    .master-button button {
        font-size: 14px;
        padding: 10px 14px;
    }
    
    .abbreviated-text-expander-main-panel {
        flex-direction: row;
        min-height: 400px;
    }
    
    .abbreviated-text-expander-selection-info {
        width: 300px;
        height: auto;
    }
}

@media (max-width: 768px) {
    .abbreviated-text-expander-main-panel {
        flex-direction: column;
    }
    
    .abbreviated-text-expander-selection-info {
        width: 100%;
        height: 150px;
    }
    
    .expansion-preview-content {
        max-height: 60vh;
    }
    
    .preview-controls {
        flex-direction: column;
        gap: 8px;
    }
    
    .preview-controls .control-button {
        width: 100%;
    }
}

/* Scripture Formatting Comparison Styles */
.comparison-view {
    background-color: var(--frame-bg-color);
    border-radius: 8px;
    padding: 20px;
    border: 2px solid var(--accent-color);
    margin: 10px 0;
}

.comparison-section {
    margin-bottom: 20px;
}

.comparison-section h4 {
    color: var(--accent-color);
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.text-preview {
    background-color: var(--bg-color);
    border: 1px solid rgba(176, 158, 128, 0.3);
    border-radius: 6px;
    padding: 15px;
    font-family: var(--font-family);
    line-height: 1.6;
    min-height: 60px;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.text-preview.original {
    border-left: 4px solid #e74c3c;
    background-color: rgba(231, 76, 60, 0.05);
}

.text-preview.formatted {
    border-left: 4px solid #27ae60;
    background-color: rgba(39, 174, 96, 0.05);
}

.text-preview strong {
    font-weight: 700;
    color: var(--accent-color);
}

.comparison-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid rgba(176, 158, 128, 0.2);
}

.comparison-actions .control-button {
    flex: 1;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.comparison-actions .control-button.accent-button {
    background-color: var(--accent-color);
    color: var(--bg-color);
    border: 2px solid var(--accent-color);
}

.comparison-actions .control-button.accent-button:hover {
    background-color: transparent;
    color: var(--accent-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(176, 158, 128, 0.3);
}

.comparison-actions .control-button.secondary {
    background-color: transparent;
    color: var(--text-color);
    border: 2px solid rgba(176, 158, 128, 0.4);
}

.comparison-actions .control-button.secondary:hover {
    background-color: rgba(176, 158, 128, 0.1);
    border-color: var(--accent-color);
    transform: translateY(-1px);
}

/* Enhanced Selection Display */
.selection-display .comparison-view {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Difference highlighting styles */
.diff-added {
    background-color: rgba(39, 174, 96, 0.2);
    color: #27ae60;
    font-weight: 700;
    padding: 2px 4px;
    border-radius: 3px;
    border: 1px solid rgba(39, 174, 96, 0.4);
}

.diff-changed {
    background-color: rgba(255, 193, 7, 0.2);
    color: #f39c12;
    font-weight: 700;
    padding: 2px 4px;
    border-radius: 3px;
    border: 1px solid rgba(255, 193, 7, 0.4);
}

.diff-removed {
    background-color: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    font-weight: 700;
    padding: 2px 4px;
    border-radius: 3px;
    border: 1px solid rgba(231, 76, 60, 0.4);
    text-decoration: line-through;
}

/* Enhanced text preview with better difference visibility */
.text-preview {
    background-color: var(--bg-color);
    border: 1px solid rgba(176, 158, 128, 0.3);
    border-radius: 6px;
    padding: 15px;
    font-family: var(--font-family);
    line-height: 1.8;
    min-height: 60px;
    word-wrap: break-word;
    white-space: pre-wrap;
    position: relative;
}

.text-preview.original {
    border-left: 4px solid #e74c3c;
    background-color: rgba(231, 76, 60, 0.05);
}

.text-preview.formatted {
    border-left: 4px solid #27ae60;
    background-color: rgba(39, 174, 96, 0.05);
}

/* Color Legend Styles */
.color-legend {
    background-color: rgba(176, 158, 128, 0.1);
    border: 1px solid rgba(176, 158, 128, 0.3);
    border-radius: 6px;
    padding: 12px;
    margin: 15px 0;
    font-size: 13px;
}

.color-legend-inline {
    background-color: rgba(176, 158, 128, 0.1);
    border: 1px solid rgba(176, 158, 128, 0.3);
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 15px;
    font-size: 12px;
}

.legend-title {
    font-weight: 600;
    color: var(--accent-color);
    margin-bottom: 8px;
    font-size: 13px;
}

.legend-items {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.legend-items-inline {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--text-color);
}

.legend-item-inline {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 11px;
    color: var(--text-color);
}

/* Make legend samples more visible */
.legend-item .diff-added,
.legend-item .diff-changed,
.legend-item .diff-removed,
.legend-item-inline .diff-added,
.legend-item-inline .diff-changed,
.legend-item-inline .diff-removed {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 600;
}

/* Mobile responsiveness for comparison view */
@media (max-width: 768px) {
    .comparison-actions {
        flex-direction: column;
    }

    .comparison-actions .control-button {
        width: 100%;
    }

    .text-preview {
        font-size: 14px;
        padding: 12px;
        line-height: 1.6;
    }

    .comparison-section h4 {
        font-size: 13px;
    }

    .diff-added, .diff-changed, .diff-removed {
        padding: 1px 3px;
        font-size: 13px;
    }
}
