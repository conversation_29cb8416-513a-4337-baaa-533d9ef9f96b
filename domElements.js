// --- DOM Elements ---
export let audioPlayer;
export let musicFolderInput;
export let docFolderInput;
export let prevTrackBtn;
export let playPauseBtn;
export let nextTrackBtn;
export let progressBar;
export let currentTimeDisplay;
export let durationDisplay;
export let volumeIcon;
export let volumeSlider;

export let reprocessGTTSBtn;
export let gTtsVoiceSelect;
export let reprocessMsTTSBtn;
export let msTtsVoiceSelect;

export let musicPlaylistEl;
export let noMusicMessage;
export let docListEl;
export let noDocsMessage;
export let docContentHeading;
export let toggleEditBtn;
export let saveDocBtn;
export let saveToFolderBtn;
export let debugDocxBtn;
export let docViewerEditor;
export let docPlaceholder;
export let docEditorTextarea;
export let docHtmlViewer;
export let breakTagButtonsEl;
export let statusBar;

// Main view navigation elements
export let appContainer;
export let audiobookVerificationBtn;
export let audiobookTextEditorBtn;
export let textEditorViewContainer;
export let aiVoiceCreatorBtn;
export let aiVoiceCreatorViewContainer;
export let quoteFootnoteInserterBtn;
export let quoteFootnoteInserterViewContainer;
export let pdfToTextConverterBtn;
export let pdfToTextConverterViewContainer;
export let abbreviatedTextExpanderBtn;
export let abbreviatedTextExpanderViewContainer;

// Theme selector
export let themeSelect;

// SSML Editor specific DOM elements
export let ssmlEditorHeading;
export let ssmlEditorFileInfo;
export let ssmlFileInput;
export let ssmlFileInputLabel;
export let ssmlAddParagraphBtn;
export let ssmlAddSentenceBtn;
export let ssmlInsertBreakpointBtn;
export let ssmlPreviewBtn;
export let ssmlPauseQuoteBtn;
export let ssmlPauseFootnoteBtn;
export let ssmlBreakLongSentenceBtn;
export let ssmlSetMaxLengthBtn;
export let ssmlSetLongSentenceThresholdBtn;
export let ssmlSaveBtn;
export let ssmlTextWidget;
export let ssmlFindInput;
export let ssmlReplaceInput;
export let ssmlFindNextBtn;
export let ssmlReplaceBtn;
export let ssmlReplaceAllBtn;
export let ssmlStatusBar;

// AI Verification DOM Elements
export let whisperAudioCheckBtn;

// View SSML Text Modal Elements
export let viewSSMLTextModal;
export let viewSSMLTextModalTitle;
export let viewSSMLModalContentArea;
export let viewSSMLTextModalCloseBtn;

// Whisper Audio Check Modal Elements
export let whisperAudioCheckModal;
export let whisperSingleFileRadio;
export let whisperAllFilesRadio;
export let whisperFileSelect;
export let whisperSingleFileControls;
export let whisperModelSelect;
export let whisperOutputArea;
export let whisperStartCheckBtn;
export let whisperModalCloseBtn;

// AI Voice Creator DOM Elements
export let aiVoiceModeSingleRadio;
export let aiVoiceModeFolderRadio;
export let aiVoiceSingleInputControls;
export let aiVoiceFolderInputControls;
export let aiVoiceSSMLFileInput;
export let aiVoiceSSMLFileInputLabel;
export let aiVoiceSSMLFolderInput;
export let aiVoiceSSMLFolderInputLabel;
export let aiVoiceLoadFromEditorBtn;
export let aiVoiceTTSProviderSelect;
export let aiVoiceTTSVoiceSelect;
export let aiVoiceSpeedSlider;
export let aiVoiceSpeedValueDisplay;
export let aiVoiceEncodingSelect;
export let aiVoiceFilenamePrefix;
export let aiVoiceSSMLTextarea;
export let aiVoiceTextareaContainer;
export let aiVoiceSynthesizeBtn;
export let aiVoiceStatusLog;
export let aiVoiceSSMLLoadedFileInfo;

// Quote and Footnote Inserter DOM Elements
export let quoteFootnoteFileInput;
export let quoteFootnoteFileInputLabel;
export let quoteFootnoteSaveBtn;
export let quoteFootnoteAddQuoteMarkersBtn;
export let quoteFootnoteAddFootnoteMarkersBtn;
export let quoteFootnoteTextArea;
export let quoteFootnoteAnnotationsList;
export let quoteFootnoteFileInfo;
export let quoteFootnoteStatusBar;

// PDF to Text Converter DOM Elements
export let ocrEngineSelect;
export let azureConfigGroup;
export let azureDiEndpoint;
export let azureDiKey;
export let testAzureConnectionBtn;
export let mistralConfigGroup;
export let mistralAutoStartStatus;
export let mistralUrl;
export let mistralApiKey;
export let mistralModel;
export let huggingfaceToken;
export let testMistralConnectionBtn;
export let setupRealMistralBtn;
export let checkRequirementsBtn;
export let ollamaConfigGroup;
export let ollamaAutoStartStatus;
export let ollamaUrl;
export let ollamaModel;
export let ollamaFastModeCheckbox;
export let testOllamaConnectionBtn;
export let setupOllamaBtn;
export let pdfFileInput;
export let pdfFileInputLabel;
export let pdfFileDetails;
export let imageDpiSelect;
export let saveImagesCheckbox;
export let startConversionBtn;
export let downloadResultBtn;
export let pdfPreviewContainer;
export let conversionProgress;
export let pdfProgressBar;
export let pdfProgressText;
export let conversionLog;
export let pdfToTextFileInfo;
export let pdfToTextStatusBar;

// Abbreviated Text Expander specific DOM elements
export let abbreviatedTextExpanderFileInput;
export let abbreviatedTextExpanderFileInputLabel;
export let abbreviatedTextExpanderSaveBtn;
export let geminiApiKeyInput;
export let testGeminiConnectionBtn;
export let geminiModelSelect;
export let expandAbbreviatedTextBtn;
export let abbreviatedTextExpanderDocumentContainer;
export let abbreviatedTextExpanderTextArea;

export let abbreviatedTextExpanderFileInfo;
export let abbreviatedTextExpanderStatusBar;
export let textExpansionPreviewModal;
export let originalTextDisplay;
export let expandedTextDisplay;
export let acceptExpansionBtn;
export let rejectExpansionBtn;
export let closePreviewBtn;
export let documentWordCount;
export let documentCharCount;
export let documentOverlay;
export let selectAllTextBtn;
export let clearSelectionBtn;
export let undoChangesBtn;
export let redoChangesBtn;

export let promptTemplateSelect;
export let customInstructionsContainer;
export let customInstructionsInput;
export let currentInstructionsPreview;
export let formatScriptureBtn;
export let formatQuotesBtn;
export let expandAbbreviationsBtn;
export let formatPapalSaintsBtn;
export let formatFootnotesBtn;
export let runAllFormattingBtn;
export let cancelRunAllBtn;
export let runAllProgress;
export let progressFill;
export let progressText;


export function initDOMelements() {
    console.log("Initializing DOM elements...");

    audioPlayer = document.getElementById('audio-player');
    musicFolderInput = document.getElementById('music-folder-input');
    docFolderInput = document.getElementById('doc-folder-input');
    prevTrackBtn = document.getElementById('prev-track-btn');
    playPauseBtn = document.getElementById('play-pause-btn');
    nextTrackBtn = document.getElementById('next-track-btn');
    progressBar = document.getElementById('progress-bar');
    currentTimeDisplay = document.getElementById('current-time-display');
    durationDisplay = document.getElementById('duration-display');
    volumeIcon = document.getElementById('volume-icon');
    volumeSlider = document.getElementById('volume-slider');
    
    reprocessGTTSBtn = document.getElementById('reprocess-g-tts-btn');
    gTtsVoiceSelect = document.getElementById('g-tts-voice-select');
    reprocessMsTTSBtn = document.getElementById('reprocess-ms-tts-btn');
    msTtsVoiceSelect = document.getElementById('ms-tts-voice-select');
    
    musicPlaylistEl = document.getElementById('music-playlist');
    noMusicMessage = document.getElementById('no-music-message');
    docListEl = document.getElementById('doc-list');
    noDocsMessage = document.getElementById('no-docs-message');
    docContentHeading = document.getElementById('doc-content-heading');
    toggleEditBtn = document.getElementById('toggle-edit-btn');
    saveDocBtn = document.getElementById('save-doc-btn');
    saveToFolderBtn = document.getElementById('save-to-folder-btn');
    debugDocxBtn = document.getElementById('debug-docx-btn');
    docViewerEditor = document.getElementById('document-viewer-editor');
    docPlaceholder = document.getElementById('doc-placeholder');
    docEditorTextarea = document.getElementById('doc-editor-textarea');
    docHtmlViewer = document.getElementById('doc-html-viewer');
    breakTagButtonsEl = document.getElementById('break-tag-buttons');
    statusBar = document.getElementById('status-bar');

    appContainer = document.getElementById('app-container');
    audiobookVerificationBtn = document.getElementById('audiobook-verification-btn');
    audiobookTextEditorBtn = document.getElementById('audiobook-text-editor-btn');
    textEditorViewContainer = document.getElementById('text-editor-view-container');
    aiVoiceCreatorBtn = document.getElementById('ai-voice-creator-btn');
    aiVoiceCreatorViewContainer = document.getElementById('ai-voice-creator-view-container');
    quoteFootnoteInserterBtn = document.getElementById('quote-footnote-inserter-btn');
    quoteFootnoteInserterViewContainer = document.getElementById('quote-footnote-inserter-view-container');
    pdfToTextConverterBtn = document.getElementById('pdf-to-text-converter-btn');
    pdfToTextConverterViewContainer = document.getElementById('pdf-to-text-converter-view-container');
    abbreviatedTextExpanderBtn = document.getElementById('abbreviated-text-expander-btn');
    abbreviatedTextExpanderViewContainer = document.getElementById('abbreviated-text-expander-view-container');

    themeSelect = document.getElementById('theme-select');

    ssmlEditorHeading = document.getElementById('ssml-editor-heading');
    ssmlEditorFileInfo = document.getElementById('ssml-editor-file-info');
    ssmlFileInput = document.getElementById('ssml-file-input');
    ssmlFileInputLabel = document.getElementById('ssml-file-input-label');
    ssmlAddParagraphBtn = document.getElementById('ssml-add-paragraph-btn');
    ssmlAddSentenceBtn = document.getElementById('ssml-add-sentence-btn');
    ssmlInsertBreakpointBtn = document.getElementById('ssml-insert-breakpoint-btn');
    ssmlPreviewBtn = document.getElementById('ssml-preview-btn');
    ssmlPauseQuoteBtn = document.getElementById('ssml-pause-quote-btn');
    ssmlPauseFootnoteBtn = document.getElementById('ssml-pause-footnote-btn');
    ssmlBreakLongSentenceBtn = document.getElementById('ssml-break-long-sentence-btn');
    ssmlSetMaxLengthBtn = document.getElementById('ssml-set-max-length-btn');
    ssmlSetLongSentenceThresholdBtn = document.getElementById('ssml-set-long-sentence-threshold-btn');
    ssmlSaveBtn = document.getElementById('ssml-save-btn');
    ssmlTextWidget = document.getElementById('ssml-text-widget');
    ssmlFindInput = document.getElementById('ssml-find-input');
    ssmlReplaceInput = document.getElementById('ssml-replace-input');
    ssmlFindNextBtn = document.getElementById('ssml-find-next-btn');
    ssmlReplaceBtn = document.getElementById('ssml-replace-btn');
    ssmlReplaceAllBtn = document.getElementById('ssml-replace-all-btn');
    ssmlStatusBar = document.getElementById('ssml-status-bar');

    whisperAudioCheckBtn = document.getElementById('whisper-audio-check-btn');

    viewSSMLTextModal = document.getElementById('view-ssml-text-modal');
    viewSSMLTextModalTitle = document.getElementById('view-ssml-text-modal-title');
    viewSSMLModalContentArea = document.getElementById('view-ssml-modal-content-area').querySelector('pre');
    viewSSMLTextModalCloseBtn = document.getElementById('view-ssml-text-modal-close-btn');

    // Whisper Audio Check Modal
    whisperAudioCheckModal = document.getElementById('whisper-audio-check-modal');
    whisperSingleFileRadio = document.getElementById('whisper-single-file-radio');
    whisperAllFilesRadio = document.getElementById('whisper-all-files-radio');
    whisperFileSelect = document.getElementById('whisper-file-select');
    whisperSingleFileControls = document.getElementById('whisper-single-file-controls');
    whisperModelSelect = document.getElementById('whisper-model-select');
    whisperOutputArea = document.getElementById('whisper-output-area');
    whisperStartCheckBtn = document.getElementById('whisper-start-check-btn');
    whisperModalCloseBtn = document.getElementById('whisper-modal-close-btn');

    aiVoiceModeSingleRadio = document.getElementById('ai-voice-mode-single-radio');
    aiVoiceModeFolderRadio = document.getElementById('ai-voice-mode-folder-radio');
    aiVoiceSingleInputControls = document.getElementById('ai-voice-single-input-controls');
    aiVoiceFolderInputControls = document.getElementById('ai-voice-folder-input-controls');
    aiVoiceSSMLFileInput = document.getElementById('ai-voice-ssml-file-input');
    aiVoiceSSMLFileInputLabel = document.getElementById('ai-voice-ssml-file-input-label');
    aiVoiceSSMLFolderInput = document.getElementById('ai-voice-ssml-folder-input');
    aiVoiceSSMLFolderInputLabel = document.getElementById('ai-voice-ssml-folder-input-label');
    aiVoiceLoadFromEditorBtn = document.getElementById('ai-voice-load-from-editor-btn');
    aiVoiceTTSProviderSelect = document.getElementById('ai-voice-tts-provider-select');
    aiVoiceTTSVoiceSelect = document.getElementById('ai-voice-tts-voice-select');
    aiVoiceSpeedSlider = document.getElementById('ai-voice-speed-slider');
    aiVoiceSpeedValueDisplay = document.getElementById('ai-voice-speed-value-display');
    aiVoiceEncodingSelect = document.getElementById('ai-voice-encoding-select');
    aiVoiceFilenamePrefix = document.getElementById('ai-voice-filename-prefix');
    aiVoiceSSMLTextarea = document.getElementById('ai-voice-ssml-textarea');
    aiVoiceTextareaContainer = document.getElementById('ai-voice-textarea-container');
    aiVoiceSynthesizeBtn = document.getElementById('ai-voice-synthesize-btn');
    aiVoiceStatusLog = document.getElementById('ai-voice-status-log');
    aiVoiceSSMLLoadedFileInfo = document.getElementById('ai-voice-ssml-loaded-file-info');

    // Quote and Footnote Inserter elements
    quoteFootnoteFileInput = document.getElementById('quote-footnote-file-input');
    quoteFootnoteFileInputLabel = document.getElementById('quote-footnote-file-input-label');
    quoteFootnoteSaveBtn = document.getElementById('quote-footnote-save-btn');
    quoteFootnoteAddQuoteMarkersBtn = document.getElementById('quote-footnote-add-quote-markers-btn');
    quoteFootnoteAddFootnoteMarkersBtn = document.getElementById('quote-footnote-add-footnote-markers-btn');
    quoteFootnoteTextArea = document.getElementById('quote-footnote-text-area');
    quoteFootnoteAnnotationsList = document.getElementById('quote-footnote-annotations-list');
    quoteFootnoteFileInfo = document.getElementById('quote-footnote-file-info');
    quoteFootnoteStatusBar = document.getElementById('quote-footnote-status-bar');

    // PDF to Text Converter elements
    ocrEngineSelect = document.getElementById('ocr-engine-select');
    azureConfigGroup = document.getElementById('azure-config-group');
    azureDiEndpoint = document.getElementById('azure-di-endpoint');
    azureDiKey = document.getElementById('azure-di-key');
    testAzureConnectionBtn = document.getElementById('test-azure-connection-btn');
    mistralConfigGroup = document.getElementById('mistral-config-group');
    mistralAutoStartStatus = document.getElementById('mistral-auto-start-status');
    mistralUrl = document.getElementById('mistral-url');
    mistralApiKey = document.getElementById('mistral-api-key');
    mistralModel = document.getElementById('mistral-model');
    huggingfaceToken = document.getElementById('huggingface-token');
    testMistralConnectionBtn = document.getElementById('test-mistral-connection-btn');
    setupRealMistralBtn = document.getElementById('setup-real-mistral-btn');
    checkRequirementsBtn = document.getElementById('check-requirements-btn');
    ollamaConfigGroup = document.getElementById('ollama-config-group');
    ollamaAutoStartStatus = document.getElementById('ollama-auto-start-status');
    ollamaUrl = document.getElementById('ollama-url');
    ollamaModel = document.getElementById('ollama-model');
    ollamaFastModeCheckbox = document.getElementById('ollama-fast-mode');
    testOllamaConnectionBtn = document.getElementById('test-ollama-connection-btn');
    setupOllamaBtn = document.getElementById('setup-ollama-btn');
    pdfFileInput = document.getElementById('pdf-file-input');
    pdfFileInputLabel = document.getElementById('pdf-file-input-label');
    pdfFileDetails = document.getElementById('pdf-file-details');
    imageDpiSelect = document.getElementById('image-dpi');
    saveImagesCheckbox = document.getElementById('save-images');
    startConversionBtn = document.getElementById('start-conversion-btn');
    downloadResultBtn = document.getElementById('download-result-btn');
    pdfPreviewContainer = document.getElementById('pdf-preview-container');
    conversionProgress = document.getElementById('conversion-progress');
    pdfProgressBar = document.getElementById('pdf-progress-bar');
    pdfProgressText = document.getElementById('pdf-progress-text');
    conversionLog = document.getElementById('conversion-log');
    pdfToTextFileInfo = document.getElementById('pdf-to-text-file-info');
    pdfToTextStatusBar = document.getElementById('pdf-to-text-status-bar');

    // Abbreviated Text Expander elements
    abbreviatedTextExpanderFileInput = document.getElementById('abbreviated-text-expander-file-input');
    abbreviatedTextExpanderFileInputLabel = document.getElementById('abbreviated-text-expander-file-input-label');
    abbreviatedTextExpanderSaveBtn = document.getElementById('abbreviated-text-expander-save-btn');
    geminiApiKeyInput = document.getElementById('gemini-api-key-input');
    testGeminiConnectionBtn = document.getElementById('test-gemini-connection-btn');
    geminiModelSelect = document.getElementById('gemini-model-select');
    expandAbbreviatedTextBtn = document.getElementById('expand-abbreviated-text-btn');
    abbreviatedTextExpanderDocumentContainer = document.getElementById('abbreviated-text-expander-document-container');
    abbreviatedTextExpanderTextArea = document.getElementById('abbreviated-text-expander-text-area');

    abbreviatedTextExpanderFileInfo = document.getElementById('abbreviated-text-expander-file-info');
    abbreviatedTextExpanderStatusBar = document.getElementById('abbreviated-text-expander-status-bar');
    textExpansionPreviewModal = document.getElementById('text-expansion-preview-modal');
    originalTextDisplay = document.getElementById('original-text-display');
    expandedTextDisplay = document.getElementById('expanded-text-display');
    acceptExpansionBtn = document.getElementById('accept-expansion-btn');
    rejectExpansionBtn = document.getElementById('reject-expansion-btn');
    closePreviewBtn = document.getElementById('close-preview-btn');
    documentWordCount = document.getElementById('document-word-count');
    documentCharCount = document.getElementById('document-char-count');
    documentOverlay = document.getElementById('document-overlay');
    selectAllTextBtn = document.getElementById('select-all-text-btn');
    clearSelectionBtn = document.getElementById('clear-selection-btn');
    undoChangesBtn = document.getElementById('undo-changes-btn');
    redoChangesBtn = document.getElementById('redo-changes-btn');

    promptTemplateSelect = document.getElementById('prompt-template-select');
    customInstructionsContainer = document.getElementById('custom-instructions-container');
    customInstructionsInput = document.getElementById('custom-instructions-input');
    currentInstructionsPreview = document.getElementById('current-instructions-preview');
    formatScriptureBtn = document.getElementById('format-scripture-btn');
    formatQuotesBtn = document.getElementById('format-quotes-btn');
    expandAbbreviationsBtn = document.getElementById('expand-abbreviations-btn');
    formatPapalSaintsBtn = document.getElementById('format-papal-saints-btn');
    formatFootnotesBtn = document.getElementById('format-footnotes-btn');
    runAllFormattingBtn = document.getElementById('run-all-formatting-btn');
    cancelRunAllBtn = document.getElementById('cancel-run-all-btn');
    runAllProgress = document.getElementById('run-all-progress');
    progressFill = document.querySelector('.progress-fill');
    progressText = document.querySelector('.progress-text');

    // Debug: Log navigation elements
    console.log("Navigation elements found:");
    console.log("- audiobookVerificationBtn:", !!audiobookVerificationBtn);
    console.log("- audiobookTextEditorBtn:", !!audiobookTextEditorBtn);
    console.log("- aiVoiceCreatorBtn:", !!aiVoiceCreatorBtn);
    console.log("- quoteFootnoteInserterBtn:", !!quoteFootnoteInserterBtn);
    console.log("- pdfToTextConverterBtn:", !!pdfToTextConverterBtn);
    console.log("- abbreviatedTextExpanderBtn:", !!abbreviatedTextExpanderBtn);
    console.log("- appContainer:", !!appContainer);
    console.log("- textEditorViewContainer:", !!textEditorViewContainer);
    console.log("- aiVoiceCreatorViewContainer:", !!aiVoiceCreatorViewContainer);
    console.log("- quoteFootnoteInserterViewContainer:", !!quoteFootnoteInserterViewContainer);
    console.log("- pdfToTextConverterViewContainer:", !!pdfToTextConverterViewContainer);
    console.log("- abbreviatedTextExpanderViewContainer:", !!abbreviatedTextExpanderViewContainer);
    console.log("DOM elements initialization complete.");
}

export function initNavigationEventListeners() {
    // Debug: Check if elements exist
    console.log("DOM Elements Check:");
    console.log("audiobookVerificationBtn:", audiobookVerificationBtn);
    console.log("audiobookTextEditorBtn:", audiobookTextEditorBtn);
    console.log("aiVoiceCreatorBtn:", aiVoiceCreatorBtn);
    console.log("quoteFootnoteInserterBtn:", quoteFootnoteInserterBtn);
    console.log("pdfToTextConverterBtn:", pdfToTextConverterBtn);
    console.log("abbreviatedTextExpanderBtn:", abbreviatedTextExpanderBtn);
    console.log("appContainer:", appContainer);
    console.log("textEditorViewContainer:", textEditorViewContainer);
    console.log("aiVoiceCreatorViewContainer:", aiVoiceCreatorViewContainer);
    console.log("quoteFootnoteInserterViewContainer:", quoteFootnoteInserterViewContainer);
    console.log("pdfToTextConverterViewContainer:", pdfToTextConverterViewContainer);
    console.log("abbreviatedTextExpanderViewContainer:", abbreviatedTextExpanderViewContainer);

    // Check which elements are missing and continue with available ones
    const missingElements = [];
    if (!audiobookVerificationBtn) missingElements.push('audiobookVerificationBtn');
    if (!audiobookTextEditorBtn) missingElements.push('audiobookTextEditorBtn');
    if (!aiVoiceCreatorBtn) missingElements.push('aiVoiceCreatorBtn');
    if (!quoteFootnoteInserterBtn) missingElements.push('quoteFootnoteInserterBtn');
    if (!pdfToTextConverterBtn) missingElements.push('pdfToTextConverterBtn');
    if (!abbreviatedTextExpanderBtn) missingElements.push('abbreviatedTextExpanderBtn');
    if (!appContainer) missingElements.push('appContainer');
    if (!textEditorViewContainer) missingElements.push('textEditorViewContainer');
    if (!aiVoiceCreatorViewContainer) missingElements.push('aiVoiceCreatorViewContainer');
    if (!quoteFootnoteInserterViewContainer) missingElements.push('quoteFootnoteInserterViewContainer');
    if (!pdfToTextConverterViewContainer) missingElements.push('pdfToTextConverterViewContainer');
    if (!abbreviatedTextExpanderViewContainer) missingElements.push('abbreviatedTextExpanderViewContainer');

    if (missingElements.length > 0) {
        console.warn("Some navigation elements are missing:", missingElements);
        console.warn("Navigation will continue with available elements only.");
    }

    // Only proceed if we have at least the basic elements
    if (!appContainer) {
        console.error("Critical error: appContainer not found. Navigation cannot be initialized.");
        return;
    }

    // Create arrays with only available elements
    const navButtons = [audiobookVerificationBtn, audiobookTextEditorBtn, aiVoiceCreatorBtn, quoteFootnoteInserterBtn, pdfToTextConverterBtn, abbreviatedTextExpanderBtn].filter(btn => btn);
    const views = [appContainer, textEditorViewContainer, aiVoiceCreatorViewContainer, quoteFootnoteInserterViewContainer, pdfToTextConverterViewContainer, abbreviatedTextExpanderViewContainer].filter(view => view);

    const switchView = (viewToShow, buttonToActivate) => {
        console.log("switchView called:", viewToShow?.id, buttonToActivate?.id);

        // Hide all views by adding the 'hidden-view' class
        views.forEach(view => {
            view.classList.add('hidden-view');
            console.log("Hidden view:", view.id);
        });

        // Show the target view by removing the class
        if (viewToShow) {
            viewToShow.classList.remove('hidden-view');
            console.log("Showed view:", viewToShow.id);
        }

        // Update the active state on the navigation buttons
        navButtons.forEach(button => {
            button.classList.remove('active-nav-button');
            console.log("Deactivated button:", button.id);
        });
        if (buttonToActivate) {
            buttonToActivate.classList.add('active-nav-button');
            console.log("Activated button:", buttonToActivate.id);
        }
    };

    // Add click event listeners to the navigation buttons
    console.log("Adding navigation event listeners...");
    console.log("Available buttons:", {
        audiobookVerificationBtn: !!audiobookVerificationBtn,
        audiobookTextEditorBtn: !!audiobookTextEditorBtn,
        aiVoiceCreatorBtn: !!aiVoiceCreatorBtn,
        quoteFootnoteInserterBtn: !!quoteFootnoteInserterBtn,
        pdfToTextConverterBtn: !!pdfToTextConverterBtn,
        abbreviatedTextExpanderBtn: !!abbreviatedTextExpanderBtn
    });

    if (audiobookVerificationBtn && appContainer) {
        audiobookVerificationBtn.addEventListener('click', () => {
            console.log("Verification button clicked!");
            switchView(appContainer, audiobookVerificationBtn);
        });
        console.log("✅ Verification button listener added");
    } else {
        console.warn("❌ Cannot add verification button listener - missing elements", {
            audiobookVerificationBtn: !!audiobookVerificationBtn,
            appContainer: !!appContainer
        });
    }

    if (audiobookTextEditorBtn && textEditorViewContainer) {
        audiobookTextEditorBtn.addEventListener('click', () => {
            console.log("Text Editor button clicked!");
            switchView(textEditorViewContainer, audiobookTextEditorBtn);
        });
        console.log("✅ Text Editor button listener added");
    } else {
        console.warn("❌ Cannot add text editor button listener - missing elements", {
            audiobookTextEditorBtn: !!audiobookTextEditorBtn,
            textEditorViewContainer: !!textEditorViewContainer
        });
    }

    if (aiVoiceCreatorBtn && aiVoiceCreatorViewContainer) {
        aiVoiceCreatorBtn.addEventListener('click', () => {
            console.log("AI Voice Creator button clicked!");
            switchView(aiVoiceCreatorViewContainer, aiVoiceCreatorBtn);
        });
        console.log("✅ AI Voice Creator button listener added");
    } else {
        console.warn("❌ Cannot add AI Voice Creator button listener - missing elements");
    }

    if (quoteFootnoteInserterBtn && quoteFootnoteInserterViewContainer) {
        quoteFootnoteInserterBtn.addEventListener('click', () => {
            console.log("Quote and Footnote Inserter button clicked!");
            switchView(quoteFootnoteInserterViewContainer, quoteFootnoteInserterBtn);
        });
        console.log("✅ Quote and Footnote Inserter button listener added");
    } else {
        console.warn("❌ Cannot add Quote and Footnote Inserter button listener - missing elements");
    }

    if (pdfToTextConverterBtn && pdfToTextConverterViewContainer) {
        pdfToTextConverterBtn.addEventListener('click', () => {
            console.log("PDF to Text Converter button clicked!");
            switchView(pdfToTextConverterViewContainer, pdfToTextConverterBtn);
        });
        console.log("✅ PDF to Text Converter button listener added");
    } else {
        console.warn("❌ Cannot add PDF to Text Converter button listener - missing elements");
    }

    if (abbreviatedTextExpanderBtn && abbreviatedTextExpanderViewContainer) {
        abbreviatedTextExpanderBtn.addEventListener('click', () => {
            console.log("Abbreviated Text Expander button clicked!");
            switchView(abbreviatedTextExpanderViewContainer, abbreviatedTextExpanderBtn);
        });
        console.log("✅ Abbreviated Text Expander button listener added");
    } else {
        console.warn("❌ Cannot add Abbreviated Text Expander button listener - missing elements");
    }

    // Set the initial view when the application loads
    // We'll default to the main audiobook player/verification view.
    if (appContainer && audiobookVerificationBtn) {
        console.log("Setting initial view to verification");
        switchView(appContainer, audiobookVerificationBtn);
    } else {
        console.error("Cannot set initial view - missing appContainer or audiobookVerificationBtn");
    }

    console.log("Navigation initialization completed successfully");
}