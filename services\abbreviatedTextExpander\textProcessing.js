/********************************************************************
 *  textProcessing.js - Text Processing Module
 *  ---------------------------------------------------------------
 *  All text formatting and processing functions
 *******************************************************************/

import * as dom from '../../domElements.js';
import { getCurrentDocument, getSelectedText, getSelectedRange, getGeminiApiKey, updateStatus, handleDocumentChange } from './core.js';
import { GEMINI_MODELS, DEFAULT_GEMINI_MODEL } from '../../constants.js';

/**
 * Initialize text processing
 */
export function initializeTextProcessing() {
    console.log('Initializing text processing...');
    
    // Set up event listeners for all formatting buttons
    if (dom.formatScriptureBtn) {
        dom.formatScriptureBtn.addEventListener('click', handleFormatScripture);
    }
    
    if (dom.formatQuotesBtn) {
        dom.formatQuotesBtn.addEventListener('click', handleFormatQuotes);
    }
    
    if (dom.expandAbbreviationsBtn) {
        dom.expandAbbreviationsBtn.addEventListener('click', handleExpandAbbreviations);
    }
    
    if (dom.formatPapalSaintsBtn) {
        dom.formatPapalSaintsBtn.addEventListener('click', handleFormatPapalSaints);
    }
    
    if (dom.formatFootnotesBtn) {
        dom.formatFootnotesBtn.addEventListener('click', handleFormatFootnotes);
    }

    // Set up modal event listeners
    setupModalEventListeners();
}

/**
 * Set up event listeners for the preview modal
 */
function setupModalEventListeners() {
    const acceptBtn = document.getElementById('accept-expansion-btn');
    const rejectBtn = document.getElementById('reject-expansion-btn');

    if (acceptBtn) {
        acceptBtn.addEventListener('click', handleAcceptFormatting);
    }

    if (rejectBtn) {
        rejectBtn.addEventListener('click', handleRejectFormatting);
    }
}

/**
 * Handle accepting formatting changes from the modal
 */
function handleAcceptFormatting() {
    const expandedDisplay = document.getElementById('expanded-text-display');
    const modal = document.getElementById('text-expansion-preview-modal');

    if (expandedDisplay && modal) {
        // Check if this is scripture formatting (has markdown) or abbreviation expansion (plain text)
        const markdownText = expandedDisplay.dataset.markdownText;
        const expandedText = expandedDisplay.dataset.expandedText;

        if (markdownText) {
            // Scripture formatting - use markdown replacement
            replaceSelectedTextWithMarkdown(markdownText);
            updateStatus("Scripture formatting applied successfully.", "success");
        } else if (expandedText) {
            // Abbreviation expansion - use plain text replacement
            replaceSelectedText(expandedText);
            updateStatus("Abbreviation expansion applied successfully.", "success");
        } else {
            // Fallback to text content
            const fallbackText = expandedDisplay.textContent;
            if (fallbackText) {
                replaceSelectedText(fallbackText);
                updateStatus("Changes applied successfully.", "success");
            }
        }

        // Close modal and clear comparison view
        modal.style.display = 'none';
        clearComparisonView();

        // Reset the comparison flag if it was set by Run All Formatting
        if (window.resetRunAllComparisonFlag) {
            window.resetRunAllComparisonFlag();
        }
    }
}

/**
 * Handle rejecting formatting changes from the modal
 */
function handleRejectFormatting() {
    const modal = document.getElementById('text-expansion-preview-modal');

    if (modal) {
        // Close modal and clear comparison view
        modal.style.display = 'none';
        clearComparisonView();

        // Reset the comparison flag if it was set by Run All Formatting
        if (window.resetRunAllComparisonFlag) {
            window.resetRunAllComparisonFlag();
        }

        updateStatus("Formatting changes rejected.", "info");
    }
}

/**
 * Handle scripture formatting
 */
async function handleFormatScripture() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }

    try {
        updateStatus("Formatting scripture references...", "info");

        // Get the selected text
        const selectedText = getSelectedText();

        if (!selectedText) {
            updateStatus("Please select some text to format scripture references.", "error");
            return;
        }

        // Use AI to format scripture references
        await formatScriptureWithAI(selectedText);

    } catch (error) {
        console.error("Error formatting scripture:", error);
        updateStatus(`Failed to format scripture: ${error.message}`, "error");
    }
}

/**
 * Handle quote formatting
 */
function handleFormatQuotes() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Formatting quotes from written works...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply quote formatting
        const formattedContent = formatQuotesFromWrittenWorks(content);
        
        if (formattedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.innerHTML = formattedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Quotes from written works formatted successfully.", "success");
        } else {
            updateStatus("No qualifying quotes found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting quotes:", error);
        updateStatus(`Failed to format quotes: ${error.message}`, "error");
    }
}

/**
 * Handle abbreviation expansion
 */
async function handleExpandAbbreviations() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }

    try {
        updateStatus("Expanding abbreviations...", "info");

        // Force refresh selection first
        console.log('Forcing selection refresh...');

        // Try to get selection immediately without relying on stored state
        let selectedText = '';
        const selection = window.getSelection();
        console.log('Current selection object:', selection);
        console.log('Selection range count:', selection.rangeCount);

        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            selectedText = range.toString().trim();
            console.log('Immediate selection text:', selectedText);
            console.log('Range details:', {
                collapsed: range.collapsed,
                startContainer: range.startContainer,
                startOffset: range.startOffset,
                endContainer: range.endContainer,
                endOffset: range.endOffset
            });
        }

        // If that didn't work, try the stored selection
        if (!selectedText) {
            selectedText = getSelectedText();
            console.log('Stored selection text:', selectedText);
        }

        // If no text from core function, try direct selection
        if (!selectedText) {
            console.log('No text from getSelectedText(), trying direct selection...');
            const selection = window.getSelection();
            console.log('Direct selection object:', selection);
            console.log('Selection range count:', selection.rangeCount);

            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                selectedText = range.toString().trim();
                console.log('Direct selection text:', selectedText);
                console.log('Range collapsed:', range.collapsed);
                console.log('Range start:', range.startContainer, range.startOffset);
                console.log('Range end:', range.endContainer, range.endOffset);
            }
        }

        // If still no text, try getting from the highlighted area
        if (!selectedText) {
            console.log('No direct selection, checking for highlighted elements...');
            const highlightedElements = document.querySelectorAll('.custom-selection');
            console.log('Found highlighted elements:', highlightedElements.length);
            if (highlightedElements.length > 0) {
                selectedText = Array.from(highlightedElements).map(el => el.textContent).join(' ').trim();
                console.log('Highlighted elements text:', selectedText);
            }
        }

        console.log('Final selected text for expansion:', selectedText);

        if (!selectedText || selectedText.length === 0) {
            updateStatus("Please select some text to expand abbreviations.", "error");
            return;
        }

        // Use AI to expand abbreviations
        await expandAbbreviationsWithAI(selectedText);

    } catch (error) {
        console.error("Error expanding abbreviations:", error);
        updateStatus(`Failed to expand abbreviations: ${error.message}`, "error");
    }
}

/**
 * Handle papal saints formatting
 */
function handleFormatPapalSaints() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Formatting papal names and saint abbreviations...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply papal saints formatting
        const formattedContent = formatPapalNamesAndSaints(content);
        
        if (formattedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.textContent = formattedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Papal names and saint abbreviations formatted successfully.", "success");
        } else {
            updateStatus("No papal names or saint abbreviations found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting papal names and saints:", error);
        updateStatus(`Failed to format papal names and saints: ${error.message}`, "error");
    }
}

/**
 * Handle footnote formatting
 */
function handleFormatFootnotes() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Formatting footnotes for audiobook...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply footnote formatting
        const formattedContent = formatFootnotesForAudiobook(content);
        
        if (formattedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.textContent = formattedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Footnotes formatted for audiobook successfully.", "success");
        } else {
            updateStatus("No footnotes found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting footnotes:", error);
        updateStatus(`Failed to format footnotes: ${error.message}`, "error");
    }
}

/**
 * Replace selected text with formatted version
 */
function replaceSelectedText(formattedText) {
    const range = getSelectedRange();
    if (range && dom.abbreviatedTextExpanderTextArea) {
        // Delete the selected content
        range.deleteContents();

        // Create a document fragment to hold the formatted content
        const fragment = document.createDocumentFragment();

        // If the formatted text contains HTML, we need to parse it
        if (formattedText.includes('<strong>')) {
            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = formattedText;

            // Move all child nodes to the fragment
            while (tempDiv.firstChild) {
                fragment.appendChild(tempDiv.firstChild);
            }
        } else {
            // Plain text - create a text node
            fragment.appendChild(document.createTextNode(formattedText));
        }

        // Insert the formatted content
        range.insertNode(fragment);

        // Clear the selection
        window.getSelection().removeAllRanges();

        // Mark document as changed
        handleDocumentChange();

        console.log('Text replaced successfully');
    }
}

/**
 * Replace selected text with markdown-formatted text (converts **bold** to HTML)
 */
export function replaceSelectedTextWithMarkdown(markdownText) {
    const range = getSelectedRange();
    if (range && dom.abbreviatedTextExpanderTextArea) {
        // Delete the selected content
        range.deleteContents();

        // Create a document fragment to hold the formatted content
        const fragment = document.createDocumentFragment();

        // Convert markdown bold (**text**) to HTML
        let htmlText = markdownText;

        // Replace **text** with <strong>text</strong>
        htmlText = htmlText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // If the text contains HTML, parse it
        if (htmlText.includes('<strong>')) {
            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlText;

            // Move all child nodes to the fragment
            while (tempDiv.firstChild) {
                fragment.appendChild(tempDiv.firstChild);
            }
        } else {
            // Plain text - create a text node
            fragment.appendChild(document.createTextNode(htmlText));
        }

        // Insert the formatted content
        range.insertNode(fragment);

        // Clear the selection
        window.getSelection().removeAllRanges();

        // Mark document as changed
        handleDocumentChange();

        console.log('Markdown text replaced successfully');
    }
}

/**
 * Expand abbreviations using AI
 */
async function expandAbbreviationsWithAI(selectedText) {
    const apiKey = getGeminiApiKey();

    if (!apiKey) {
        updateStatus("Please set your Gemini API key first.", "error");
        return;
    }

    try {
        updateStatus("Expanding abbreviations with AI...", "info");

        // Get the selected model
        const selectedModel = dom.geminiModelSelect?.value || DEFAULT_GEMINI_MODEL;
        const modelConfig = GEMINI_MODELS[selectedModel];

        if (!modelConfig) {
            updateStatus(`Unknown model: ${selectedModel}`, "error");
            return;
        }

        // Build the prompt for abbreviation expansion
        const prompt = `Please expand ALL abbreviations, acronyms, and shortened words in the following text. Follow these guidelines:

1. Expand common abbreviations (e.g., "Dr." → "Doctor", "Jan." → "January", "etc." → "et cetera", "St." → "Saint")
2. Expand religious abbreviations (e.g., "O.P." → "Order of Preachers", "S.J." → "Society of Jesus")
3. Expand biblical book abbreviations (e.g., "Lev." → "Leviticus", "Gen." → "Genesis", "Mt." → "Matthew")
4. Expand acronyms when context makes the meaning clear (e.g., "USA" → "United States of America", "AI" → "Artificial Intelligence")
5. Expand shortened words (e.g., "govt" → "government", "info" → "information")
6. Preserve the original formatting, capitalization style, and punctuation
7. Be thorough - expand EVERY abbreviation you can identify
8. Return only the expanded text without any additional commentary or explanations

Text to expand:
${selectedText}`;

        const requestBody = {
            contents: [{
                parts: [{
                    text: prompt
                }]
            }],
            generationConfig: {
                temperature: 0.1,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 8192,
            }
        };

        console.log('Sending abbreviation expansion request to Gemini API...');
        console.log('Model:', modelConfig.name);
        console.log('Request body:', JSON.stringify(requestBody, null, 2));

        const response = await fetch(`${modelConfig.url}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error Response:', errorText);
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json();
        console.log('Received response from Gemini API:', data);

        if (data.candidates && data.candidates.length > 0) {
            const candidate = data.candidates[0];

            if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
                const expandedText = candidate.content.parts[0].text.trim();

                console.log('Original text:', selectedText);
                console.log('Expanded text:', expandedText);

                // Show comparison view for abbreviation expansion
                showAbbreviationExpansionPreview(selectedText, expandedText);

                updateStatus("✅ Abbreviations expanded successfully. Review the changes below.", "success");
            } else {
                console.error('No content in candidate:', candidate);

                if (candidate.finishReason) {
                    console.log('Finish reason:', candidate.finishReason);
                }

                if (candidate.safetyRatings) {
                    console.log('Safety ratings:', candidate.safetyRatings);
                }

                updateStatus("⚠️ AI response missing text content. Check console for details.", "warning");
            }
        } else {
            console.error('No candidates found in response:', data);

            // Check for error messages in the response
            if (data.error) {
                console.error('API Error:', data.error);
                updateStatus(`❌ API Error: ${data.error.message || 'Unknown error'}`, "error");
            } else {
                updateStatus("⚠️ Received unexpected response format from AI.", "warning");
            }
        }

    } catch (error) {
        console.error('Error expanding abbreviations with AI:', error);
        updateStatus(`Failed to expand abbreviations: ${error.message}`, "error");
    }
}

/**
 * Format scripture references using AI
 */
async function formatScriptureWithAI(selectedText) {
    const apiKey = getGeminiApiKey();

    if (!apiKey) {
        updateStatus("Please set your Gemini API key first.", "error");
        return;
    }

    try {
        updateStatus("Formatting scripture references with AI...", "info");

        // Get the selected model
        const selectedModel = dom.geminiModelSelect?.value || DEFAULT_GEMINI_MODEL;
        const modelConfig = GEMINI_MODELS[selectedModel];

        if (!modelConfig) {
            updateStatus(`Unknown model: ${selectedModel}`, "error");
            return;
        }

        // Build the prompt for scripture formatting
        const prompt = `Please format the following text according to these specific rules:

IMPORTANT: The text may contain both regular content AND scripture quotes. Only format the scripture quotes, leave all other text exactly as it is.

1. Look for scripture quotes within the text - these are quoted passages followed by biblical references in parentheses
2. For each scripture quote found:
   - Add "quote," at the beginning of the quoted text
   - Add "end quote," at the end of the quoted text
   - Expand abbreviated book names to full names with ordinals:
     * "1 Pet." → "First Peter", "2 Cor." → "Second Corinthians", "Lev." → "Leviticus"
     * All numbered books: "1" → "First", "2" → "Second", "3" → "Third"
   - Format chapter and verse as "chapter X, verse Y" (e.g., "17, 11" → "chapter 17, verse 11")
   - Make the ENTIRE formatted scripture bold with **bold formatting**
3. Leave all surrounding text completely unchanged
4. Return the full text with only the scripture quotes formatted

Book abbreviations to expand:
- Old Testament: Gen./Genesis, Ex./Exodus, Lev./Leviticus, Num./Numbers, Deut./Deuteronomy, Josh./Joshua, Judg./Judges, 1Sam./First Samuel, 2Sam./Second Samuel, 1Kgs./First Kings, 2Kgs./Second Kings, 1Chr./First Chronicles, 2Chr./Second Chronicles, Ezra, Neh./Nehemiah, Esth./Esther, Job, Ps./Psalms, Prov./Proverbs, Eccl./Ecclesiastes, Song/Song of Songs, Is./Isaiah, Jer./Jeremiah, Lam./Lamentations, Ezek./Ezekiel, Dan./Daniel, Hos./Hosea, Joel, Amos, Obad./Obadiah, Jon./Jonah, Mic./Micah, Nah./Nahum, Hab./Habakkuk, Zeph./Zephaniah, Hag./Haggai, Zech./Zechariah, Mal./Malachi
- New Testament: Mt./Matthew, Mk./Mark, Lk./Luke, Jn./John, Acts, Rom./Romans, 1Cor./First Corinthians, 2Cor./Second Corinthians, Gal./Galatians, Eph./Ephesians, Phil./Philippians, Col./Colossians, 1Thess./First Thessalonians, 2Thess./Second Thessalonians, 1Tim./First Timothy, 2Tim./Second Timothy, Tit./Titus, Philem./Philemon, Heb./Hebrews, Jas./James, 1Pet./First Peter, 2Pet./Second Peter, 1Jn./First John, 2Jn./Second John, 3Jn./Third John, Jude, Rev./Revelation

Book name conversion examples:
- "1 Pet." or "1 Peter" → "First Peter"
- "2 Cor." or "2 Corinthians" → "Second Corinthians"
- "1 Sam." or "1 Samuel" → "First Samuel"
- "2 Kings" → "Second Kings"
- "3 John" → "Third John"
- "Lev." → "Leviticus"
- "Gen." → "Genesis"

Examples:

Example 1 - Mixed content with embedded scripture:
Input: "The blood of Christ is infinitely precious. In the light of the Old Testament, which He fulfilled, the blood of Christ means His whole bodily life. 'You were not redeemed with corruptible things as gold or silver . . . but with the precious blood of Christ, as of a lamb unspotted and undefiled.' (1 Pet. 1, 18) This was the price of redemption."

Output: "The blood of Christ is infinitely precious. In the light of the Old Testament, which He fulfilled, the blood of Christ means His whole bodily life. **quote, You were not redeemed with corruptible things as gold or silver . . . but with the precious blood of Christ, as of a lamb unspotted and undefiled. end quote, (First Peter, chapter 1, verse 18)** This was the price of redemption."

Example 2 - Standalone scripture:
Input: "'the life of the flesh.' (Lev. 17, 11)"
Output: "**quote, the life of the flesh. end quote, (Leviticus, chapter 17, verse 11)**"

Example 3 - Text with no scripture:
Input: "This is a theological discussion without any biblical quotes."
Output: "This is a theological discussion without any biblical quotes."

Text to format:
${selectedText}

Return only the formatted text, nothing else.`;

        const response = await fetch(`${modelConfig.url}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 1,
                    topP: 0.8,
                    maxOutputTokens: 10000,
                }
            })
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error Response:', errorText);
            throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
        }

        const data = await response.json();

        console.log('AI Response:', data);
        console.log('Full candidates array:', data.candidates);

        if (data.candidates && data.candidates.length > 0) {
            const candidate = data.candidates[0];
            console.log('First candidate:', candidate);
            console.log('Candidate content:', candidate.content);
            if (candidate.content && candidate.content.parts) {
                console.log('Content parts:', candidate.content.parts);
                console.log('First part:', candidate.content.parts[0]);
            }

            // Check different possible response structures
            let formattedText = null;

            // Try the standard structure first
            if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
                formattedText = candidate.content.parts[0].text;
                console.log('Found text in standard structure');
            }
            // Try accessing content directly if it's a string
            else if (candidate.content && typeof candidate.content === 'string') {
                formattedText = candidate.content;
                console.log('Found text in candidate.content (string)');
            }
            // Try alternative structure (text directly in candidate)
            else if (candidate.text) {
                formattedText = candidate.text;
                console.log('Found text in candidate.text');
            }
            // Try another alternative (output field)
            else if (candidate.output) {
                formattedText = candidate.output;
                console.log('Found text in candidate.output');
            }
            // Check if there's a message field
            else if (candidate.message && candidate.message.content) {
                formattedText = candidate.message.content;
                console.log('Found text in candidate.message.content');
            }

            if (formattedText && formattedText.trim().length > 0) {
                formattedText = formattedText.trim();
                console.log('Formatted text:', formattedText);

                // Show the preview modal for comparison
                showScriptureFormattingPreview(selectedText, formattedText);

                updateStatus(`✅ Scripture formatting preview ready - review and accept/reject changes`, "success");
            } else {
                console.error('No text content found in any expected location');
                console.error('Candidate structure:', candidate);

                // Check for safety ratings or finish reason
                if (candidate.finishReason) {
                    console.log('Finish reason:', candidate.finishReason);
                    if (candidate.finishReason === 'SAFETY') {
                        updateStatus("⚠️ AI response blocked by safety filters. Try rephrasing your text.", "warning");
                        return;
                    }
                }

                if (candidate.safetyRatings) {
                    console.log('Safety ratings:', candidate.safetyRatings);
                }

                updateStatus("⚠️ AI response missing text content. Check console for details.", "warning");
            }
        } else {
            console.error('No candidates found in response:', data);

            // Check for error messages in the response
            if (data.error) {
                console.error('API Error:', data.error);
                updateStatus(`❌ API Error: ${data.error.message || 'Unknown error'}`, "error");
            } else {
                updateStatus("⚠️ Received unexpected response format from AI.", "warning");
            }
        }

    } catch (error) {
        console.error('Error formatting scripture with AI:', error);
        updateStatus(`Failed to format scripture: ${error.message}`, "error");
    }
}

/**
 * Show abbreviation expansion preview modal
 */
function showAbbreviationExpansionPreview(originalText, expandedText) {
    // Update the preview modal content
    const originalDisplay = document.getElementById('original-text-display');
    const expandedDisplay = document.getElementById('expanded-text-display');
    const modal = document.getElementById('text-expansion-preview-modal');
    const modalTitle = modal.querySelector('h3');

    if (originalDisplay && expandedDisplay && modal && modalTitle) {
        // Update modal title
        modalTitle.textContent = 'Abbreviation Expansion Preview';

        // Generate highlighted versions showing differences
        const { highlightedOriginal, highlightedFormatted } = highlightAbbreviationDifferences(originalText, expandedText);

        // Show highlighted original text
        originalDisplay.innerHTML = highlightedOriginal;

        // Show highlighted expanded text
        expandedDisplay.innerHTML = highlightedFormatted;

        // Store the expanded text for later use (no markdown conversion needed)
        expandedDisplay.dataset.expandedText = expandedText;

        // Add color legend to modal if it doesn't exist
        addColorLegendToModal(modal);

        // Show the modal
        modal.style.display = 'block';

        // Update the selection display as well
        updateSelectionDisplayWithAbbreviationComparison(originalText, expandedText);
    }
}

/**
 * Show scripture formatting preview modal
 */
function showScriptureFormattingPreview(originalText, formattedText) {
    // Update the preview modal content
    const originalDisplay = document.getElementById('original-text-display');
    const expandedDisplay = document.getElementById('expanded-text-display');
    const modal = document.getElementById('text-expansion-preview-modal');
    const modalTitle = modal.querySelector('h3');

    if (originalDisplay && expandedDisplay && modal && modalTitle) {
        // Update modal title
        modalTitle.textContent = 'Scripture Formatting Preview';

        // Convert markdown to HTML for display
        let htmlFormattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Generate highlighted versions showing differences
        const { highlightedOriginal, highlightedFormatted } = highlightTextDifferences(originalText, htmlFormattedText);

        // Show highlighted original text
        originalDisplay.innerHTML = highlightedOriginal;

        // Show highlighted formatted text
        expandedDisplay.innerHTML = highlightedFormatted;

        // Store the formatted text for later use
        expandedDisplay.dataset.markdownText = formattedText;

        // Add color legend to modal if it doesn't exist
        addColorLegendToModal(modal);

        // Show the modal
        modal.style.display = 'block';

        // Update the selection display as well
        updateSelectionDisplayWithComparison(originalText, formattedText);
    }
}

/**
 * Add color legend to modal
 */
function addColorLegendToModal(modal) {
    // Check if legend already exists
    let legend = modal.querySelector('.color-legend');

    if (!legend) {
        // Create legend element
        legend = document.createElement('div');
        legend.className = 'color-legend';
        legend.innerHTML = `
            <div class="legend-title">Change Indicators:</div>
            <div class="legend-items">
                <span class="legend-item">
                    <span class="diff-added">Added</span> - New text like "quote," "end quote," "chapter," "verse"
                </span>
                <span class="legend-item">
                    <span class="diff-changed">Changed</span> - Modified text like "1 Pet." → "First Peter"
                </span>
                <span class="legend-item">
                    <span class="diff-removed">Removed</span> - Deleted text (if any)
                </span>
            </div>
        `;

        // Insert legend after the title
        const title = modal.querySelector('h3');
        if (title) {
            title.insertAdjacentElement('afterend', legend);
        }
    }
}

/**
 * Update the selection display to show comparison
 */
function updateSelectionDisplayWithComparison(originalText, formattedText) {
    const selectionDisplay = document.getElementById('abbreviated-text-expander-selection-display');

    if (selectionDisplay) {
        // Convert markdown to HTML for display
        let htmlFormattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Generate highlighted versions showing differences
        const { highlightedOriginal, highlightedFormatted } = highlightTextDifferences(originalText, htmlFormattedText);

        selectionDisplay.innerHTML = `
            <div class="comparison-view">
                <div class="color-legend-inline">
                    <div class="legend-title">Change Indicators:</div>
                    <div class="legend-items-inline">
                        <span class="legend-item-inline"><span class="diff-added">Added</span></span>
                        <span class="legend-item-inline"><span class="diff-changed">Changed</span></span>
                        <span class="legend-item-inline"><span class="diff-removed">Removed</span></span>
                    </div>
                </div>
                <div class="comparison-section">
                    <h4>Original Selected Text:</h4>
                    <div class="text-preview original">${highlightedOriginal}</div>
                </div>
                <div class="comparison-section">
                    <h4>AI Formatted Result:</h4>
                    <div class="text-preview formatted">${highlightedFormatted}</div>
                </div>
                <div class="comparison-actions">
                    <button id="accept-scripture-formatting" class="control-button accent-button small">Accept Formatting</button>
                    <button id="reject-scripture-formatting" class="control-button secondary small">Reject Changes</button>
                </div>
            </div>
        `;

        // Add event listeners for the new buttons
        const acceptBtn = document.getElementById('accept-scripture-formatting');
        const rejectBtn = document.getElementById('reject-scripture-formatting');

        if (acceptBtn) {
            acceptBtn.addEventListener('click', () => {
                replaceSelectedTextWithMarkdown(formattedText);
                clearComparisonView();
                updateStatus("Scripture formatting applied successfully.", "success");
            });
        }

        if (rejectBtn) {
            rejectBtn.addEventListener('click', () => {
                clearComparisonView();
                updateStatus("Scripture formatting rejected.", "info");
            });
        }
    }
}

/**
 * Highlight differences between original and expanded text for abbreviations
 */
function highlightAbbreviationDifferences(originalText, expandedText) {
    console.log('Highlighting abbreviation differences:');
    console.log('Original:', originalText);
    console.log('Expanded:', expandedText);

    let highlightedOriginal = originalText;
    let highlightedFormatted = expandedText;

    // Simple approach: find specific abbreviations that were likely expanded
    const commonExpansions = [
        { abbrev: 'O.P.', expanded: 'Order of Preachers' },
        { abbrev: 'St.', expanded: 'Saint' },
        { abbrev: 'Lev.', expanded: 'Leviticus' },
        { abbrev: 'Gen.', expanded: 'Genesis' },
        { abbrev: 'Mt.', expanded: 'Matthew' },
        { abbrev: 'Dr.', expanded: 'Doctor' },
        { abbrev: 'Mr.', expanded: 'Mister' },
        { abbrev: 'Mrs.', expanded: 'Missus' },
        { abbrev: 'etc.', expanded: 'et cetera' }
    ];

    // Highlight known expansions
    commonExpansions.forEach(({ abbrev, expanded }) => {
        if (originalText.includes(abbrev) && expandedText.includes(expanded)) {
            // Highlight abbreviation in original
            highlightedOriginal = highlightedOriginal.replace(
                new RegExp(escapeRegex(abbrev), 'g'),
                `<span class="diff-changed">${abbrev}</span>`
            );
            // Highlight expansion in formatted
            highlightedFormatted = highlightedFormatted.replace(
                new RegExp(escapeRegex(expanded), 'g'),
                `<span class="diff-changed">${expanded}</span>`
            );
        }
    });



    return { highlightedOriginal, highlightedFormatted };
}

/**
 * Update the selection display to show abbreviation comparison
 */
function updateSelectionDisplayWithAbbreviationComparison(originalText, expandedText) {
    const selectionDisplay = document.getElementById('abbreviated-text-expander-selection-display');

    if (selectionDisplay) {
        // Generate highlighted versions showing differences
        const { highlightedOriginal, highlightedFormatted } = highlightAbbreviationDifferences(originalText, expandedText);

        selectionDisplay.innerHTML = `
            <div class="comparison-view">
                <div class="color-legend-inline">
                    <div class="legend-title">Change Indicators:</div>
                    <div class="legend-items-inline">
                        <span class="legend-item-inline"><span class="diff-changed">Expanded</span></span>
                    </div>
                </div>
                <div class="comparison-section">
                    <h4>Original Selected Text:</h4>
                    <div class="text-preview original">${highlightedOriginal}</div>
                </div>
                <div class="comparison-section">
                    <h4>AI Expanded Result:</h4>
                    <div class="text-preview formatted">${highlightedFormatted}</div>
                </div>
                <div class="comparison-actions">
                    <button id="accept-abbreviation-expansion" class="control-button accent-button small">Accept Expansion</button>
                    <button id="reject-abbreviation-expansion" class="control-button secondary small">Reject Changes</button>
                </div>
            </div>
        `;

        // Add event listeners for the new buttons
        const acceptBtn = document.getElementById('accept-abbreviation-expansion');
        const rejectBtn = document.getElementById('reject-abbreviation-expansion');

        if (acceptBtn) {
            acceptBtn.addEventListener('click', () => {
                replaceSelectedText(expandedText);
                clearComparisonView();
                updateStatus("Abbreviation expansion applied successfully.", "success");
            });
        }

        if (rejectBtn) {
            rejectBtn.addEventListener('click', () => {
                clearComparisonView();
                updateStatus("Abbreviation expansion rejected.", "info");
            });
        }
    }
}

/**
 * Escape special regex characters
 */
function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Check if this is a common abbreviation expansion
 */
function isAbbreviationExpansion(original, formatted) {
    const expansions = {
        'St.': 'Saint',
        'St': 'Saint',
        'Dr.': 'Doctor',
        'Dr': 'Doctor',
        'Prof.': 'Professor',
        'Prof': 'Professor',
        'Jan.': 'January',
        'Feb.': 'February',
        'Mar.': 'March',
        'Apr.': 'April',
        'Aug.': 'August',
        'Sep.': 'September',
        'Sept.': 'September',
        'Oct.': 'October',
        'Nov.': 'November',
        'Dec.': 'December',
        'vs.': 'versus',
        'vs': 'versus',
        'etc.': 'et cetera',
        'etc': 'et cetera',
        'i.e.': 'that is',
        'e.g.': 'for example',
        'a.m.': 'in the morning',
        'p.m.': 'in the evening',
        'AM': 'in the morning',
        'PM': 'in the evening'
    };

    return expansions[original] === formatted ||
           expansions[original.toLowerCase()] === formatted.toLowerCase();
}

/**
 * Highlight differences between original and formatted text
 */
export function highlightTextDifferences(originalText, formattedText) {
    // Remove HTML tags from formatted text for comparison
    const cleanFormattedText = formattedText.replace(/<[^>]*>/g, '');

    console.log('Highlighting differences:');
    console.log('Original:', originalText);
    console.log('Formatted:', cleanFormattedText);

    // For Run All Formatting, use pattern-based highlighting since it performs complex transformations
    // that are difficult to track with word-by-word comparison
    let highlightedOriginal = originalText;
    let highlightedFormatted = cleanFormattedText;

    // Track what changes we've made to avoid double-highlighting
    const appliedChanges = new Set();

    // 1. Highlight "quote," additions at the beginning
    if (cleanFormattedText.toLowerCase().includes('quote,') && !originalText.toLowerCase().includes('quote,')) {
        highlightedFormatted = highlightedFormatted.replace(/\bquote,\s*/gi, '<span class="diff-added">$&</span>');
        appliedChanges.add('quote-start');
    }

    // 2. Highlight "end quote," additions
    if (cleanFormattedText.toLowerCase().includes('end quote,') && !originalText.toLowerCase().includes('end quote,')) {
        highlightedFormatted = highlightedFormatted.replace(/\bend quote,\s*/gi, '<span class="diff-added">$&</span>');
        appliedChanges.add('quote-end');
    }

    // 3. Highlight biblical book name expansions
    const bookPatterns = [
        // Numbered books
        { original: /\b1\s*Pet\.?\b/gi, replacement: /\bFirst Peter\b/gi, name: '1Pet' },
        { original: /\b2\s*Pet\.?\b/gi, replacement: /\bSecond Peter\b/gi, name: '2Pet' },
        { original: /\b1\s*Cor\.?\b/gi, replacement: /\bFirst Corinthians\b/gi, name: '1Cor' },
        { original: /\b2\s*Cor\.?\b/gi, replacement: /\bSecond Corinthians\b/gi, name: '2Cor' },
        { original: /\b1\s*Sam\.?\b/gi, replacement: /\bFirst Samuel\b/gi, name: '1Sam' },
        { original: /\b2\s*Sam\.?\b/gi, replacement: /\bSecond Samuel\b/gi, name: '2Sam' },
        { original: /\b1\s*Tim\.?\b/gi, replacement: /\bFirst Timothy\b/gi, name: '1Tim' },
        { original: /\b2\s*Tim\.?\b/gi, replacement: /\bSecond Timothy\b/gi, name: '2Tim' },
        { original: /\b1\s*Thess\.?\b/gi, replacement: /\bFirst Thessalonians\b/gi, name: '1Thess' },
        { original: /\b2\s*Thess\.?\b/gi, replacement: /\bSecond Thessalonians\b/gi, name: '2Thess' },
        { original: /\b1\s*Kgs\.?\b/gi, replacement: /\bFirst Kings\b/gi, name: '1Kgs' },
        { original: /\b2\s*Kgs\.?\b/gi, replacement: /\bSecond Kings\b/gi, name: '2Kgs' },
        { original: /\b1\s*Chr\.?\b/gi, replacement: /\bFirst Chronicles\b/gi, name: '1Chr' },
        { original: /\b2\s*Chr\.?\b/gi, replacement: /\bSecond Chronicles\b/gi, name: '2Chr' },
        { original: /\b1\s*Jn\.?\b/gi, replacement: /\bFirst John\b/gi, name: '1Jn' },
        { original: /\b2\s*Jn\.?\b/gi, replacement: /\bSecond John\b/gi, name: '2Jn' },
        { original: /\b3\s*Jn\.?\b/gi, replacement: /\bThird John\b/gi, name: '3Jn' },

        // Common abbreviations
        { original: /\bMt\.?\b/gi, replacement: /\bMatthew\b/gi, name: 'Mt' },
        { original: /\bMatt\.?\b/gi, replacement: /\bMatthew\b/gi, name: 'Matt' },
        { original: /\bMk\.?\b/gi, replacement: /\bMark\b/gi, name: 'Mk' },
        { original: /\bLk\.?\b/gi, replacement: /\bLuke\b/gi, name: 'Lk' },
        { original: /\bJn\.?\b/gi, replacement: /\bJohn\b/gi, name: 'Jn' },
        { original: /\bRom\.?\b/gi, replacement: /\bRomans\b/gi, name: 'Rom' },
        { original: /\bGen\.?\b/gi, replacement: /\bGenesis\b/gi, name: 'Gen' },
        { original: /\bEx\.?\b/gi, replacement: /\bExodus\b/gi, name: 'Ex' },
        { original: /\bExod\.?\b/gi, replacement: /\bExodus\b/gi, name: 'Exod' },
        { original: /\bLev\.?\b/gi, replacement: /\bLeviticus\b/gi, name: 'Lev' },
        { original: /\bNum\.?\b/gi, replacement: /\bNumbers\b/gi, name: 'Num' },
        { original: /\bDeut\.?\b/gi, replacement: /\bDeuteronomy\b/gi, name: 'Deut' },
        { original: /\bPs\.?\b/gi, replacement: /\bPsalms\b/gi, name: 'Ps' },
        { original: /\bProv\.?\b/gi, replacement: /\bProverbs\b/gi, name: 'Prov' },
        { original: /\bIs\.?\b/gi, replacement: /\bIsaiah\b/gi, name: 'Is' },
        { original: /\bIsa\.?\b/gi, replacement: /\bIsaiah\b/gi, name: 'Isa' },
        { original: /\bJer\.?\b/gi, replacement: /\bJeremiah\b/gi, name: 'Jer' },
        { original: /\bEzek\.?\b/gi, replacement: /\bEzekiel\b/gi, name: 'Ezek' },
        { original: /\bDan\.?\b/gi, replacement: /\bDaniel\b/gi, name: 'Dan' },
        { original: /\bHos\.?\b/gi, replacement: /\bHosea\b/gi, name: 'Hos' },
        { original: /\bMic\.?\b/gi, replacement: /\bMicah\b/gi, name: 'Mic' },
        { original: /\bHab\.?\b/gi, replacement: /\bHabakkuk\b/gi, name: 'Hab' },
        { original: /\bZech\.?\b/gi, replacement: /\bZechariah\b/gi, name: 'Zech' },
        { original: /\bMal\.?\b/gi, replacement: /\bMalachi\b/gi, name: 'Mal' },
        { original: /\bGal\.?\b/gi, replacement: /\bGalatians\b/gi, name: 'Gal' },
        { original: /\bEph\.?\b/gi, replacement: /\bEphesians\b/gi, name: 'Eph' },
        { original: /\bPhil\.?\b/gi, replacement: /\bPhilippians\b/gi, name: 'Phil' },
        { original: /\bCol\.?\b/gi, replacement: /\bColossians\b/gi, name: 'Col' },
        { original: /\bHeb\.?\b/gi, replacement: /\bHebrews\b/gi, name: 'Heb' },
        { original: /\bJas\.?\b/gi, replacement: /\bJames\b/gi, name: 'Jas' },
        { original: /\bRev\.?\b/gi, replacement: /\bRevelation\b/gi, name: 'Rev' }
    ];

    bookPatterns.forEach(pattern => {
        const originalMatch = originalText.match(pattern.original);
        const formattedMatch = cleanFormattedText.match(pattern.replacement);

        if (originalMatch && formattedMatch && !appliedChanges.has(pattern.name)) {
            // Highlight the original abbreviation
            highlightedOriginal = highlightedOriginal.replace(pattern.original, '<span class="diff-changed">$&</span>');
            // Highlight the expanded form
            highlightedFormatted = highlightedFormatted.replace(pattern.replacement, '<span class="diff-changed">$&</span>');
            appliedChanges.add(pattern.name);
        }
    });

    // 4. Highlight chapter/verse formatting changes (e.g., "1, 18" → "chapter 1, verse 18")
    const chapterVerseMatches = originalText.match(/\b(\d+),?\s*(\d+)\b/g);
    if (chapterVerseMatches) {
        chapterVerseMatches.forEach(match => {
            const [, chapter, verse] = match.match(/(\d+),?\s*(\d+)/);
            const expandedForm = `chapter ${chapter}, verse ${verse}`;

            if (cleanFormattedText.includes(expandedForm) && !appliedChanges.has(`cv-${chapter}-${verse}`)) {
                // Highlight original chapter/verse
                const escapedMatch = match.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                highlightedOriginal = highlightedOriginal.replace(new RegExp(`\\b${escapedMatch}\\b`, 'g'), '<span class="diff-changed">$&</span>');
                // Highlight expanded form
                highlightedFormatted = highlightedFormatted.replace(new RegExp(expandedForm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '<span class="diff-changed">$&</span>');
                appliedChanges.add(`cv-${chapter}-${verse}`);
            }
        });
    }

    // 5. Highlight common abbreviation expansions
    const commonAbbreviations = [
        { original: /\bSt\.?\s+/gi, replacement: /\bSaint\s+/gi, name: 'Saint' },
        { original: /\bSts\.?\s+/gi, replacement: /\bSaints\s+/gi, name: 'Saints' },
        { original: /\bDr\.?\s+/gi, replacement: /\bDoctor\s+/gi, name: 'Doctor' },
        { original: /\bProf\.?\s+/gi, replacement: /\bProfessor\s+/gi, name: 'Professor' },
        { original: /\bO\.P\.?\b/gi, replacement: /\bOrder of Preachers\b/gi, name: 'OP' },
        { original: /\bS\.J\.?\b/gi, replacement: /\bSociety of Jesus\b/gi, name: 'SJ' },
        { original: /\bO\.S\.B\.?\b/gi, replacement: /\bOrder of Saint Benedict\b/gi, name: 'OSB' },
        { original: /\bJan\.?\b/gi, replacement: /\bJanuary\b/gi, name: 'January' },
        { original: /\bFeb\.?\b/gi, replacement: /\bFebruary\b/gi, name: 'February' },
        { original: /\bMar\.?\b/gi, replacement: /\bMarch\b/gi, name: 'March' },
        { original: /\bApr\.?\b/gi, replacement: /\bApril\b/gi, name: 'April' },
        { original: /\bAug\.?\b/gi, replacement: /\bAugust\b/gi, name: 'August' },
        { original: /\bSep\.?\b/gi, replacement: /\bSeptember\b/gi, name: 'September' },
        { original: /\bSept\.?\b/gi, replacement: /\bSeptember\b/gi, name: 'September2' },
        { original: /\bOct\.?\b/gi, replacement: /\bOctober\b/gi, name: 'October' },
        { original: /\bNov\.?\b/gi, replacement: /\bNovember\b/gi, name: 'November' },
        { original: /\bDec\.?\b/gi, replacement: /\bDecember\b/gi, name: 'December' }
    ];

    commonAbbreviations.forEach(abbrev => {
        const originalMatch = originalText.match(abbrev.original);
        const formattedMatch = cleanFormattedText.match(abbrev.replacement);

        if (originalMatch && formattedMatch && !appliedChanges.has(abbrev.name)) {
            // Highlight the original abbreviation
            highlightedOriginal = highlightedOriginal.replace(abbrev.original, '<span class="diff-changed">$&</span>');
            // Highlight the expanded form
            highlightedFormatted = highlightedFormatted.replace(abbrev.replacement, '<span class="diff-changed">$&</span>');
            appliedChanges.add(abbrev.name);
        }
    });

    console.log('Highlighted original:', highlightedOriginal);
    console.log('Highlighted formatted:', highlightedFormatted);

    return {
        highlightedOriginal: highlightedOriginal,
        highlightedFormatted: highlightedFormatted
    };
}

/**
 * Check if this is a book name expansion (e.g., "1 Pet." → "First Peter")
 */
function isBookNameExpansion(original, formatted) {
    const bookMappings = {
        '1': 'first', '2': 'second', '3': 'third',
        'pet.': 'peter', 'pet': 'peter',
        'cor.': 'corinthians', 'cor': 'corinthians',
        'sam.': 'samuel', 'sam': 'samuel',
        'lev.': 'leviticus', 'lev': 'leviticus',
        'gen.': 'genesis', 'gen': 'genesis',
        'tim.': 'timothy', 'tim': 'timothy',
        'thess.': 'thessalonians', 'thess': 'thessalonians'
    };

    const originalLower = original.toLowerCase();
    const formattedLower = formatted.toLowerCase();

    return bookMappings[originalLower] === formattedLower;
}

/**
 * Check if this is a chapter/verse expansion
 */
function isChapterVerseExpansion(originalWords, formattedWords, originalIndex, formattedIndex) {
    // Check if we have enough words to compare
    if (originalIndex + 2 >= originalWords.length || formattedIndex + 4 >= formattedWords.length) {
        return false;
    }

    // Look for pattern: number, comma, number → "chapter", number, comma, "verse", number
    const original = originalWords.slice(originalIndex, originalIndex + 3).join('').toLowerCase();
    const formatted = formattedWords.slice(formattedIndex, formattedIndex + 5).join('').toLowerCase();

    // Simple pattern matching for chapter/verse
    return /^\d+,\s*\d+$/.test(original.replace(/\s/g, '')) &&
           formatted.includes('chapter') && formatted.includes('verse');
}

/**
 * Clear the comparison view and return to normal selection display
 */
export function clearComparisonView() {
    const selectionDisplay = document.getElementById('abbreviated-text-expander-selection-display');
    const modal = document.getElementById('text-expansion-preview-modal');

    if (selectionDisplay) {
        selectionDisplay.innerHTML = `
            <div class="no-selection-state">
                <p>No text selected</p>
                <p class="hint">Select text in the document to expand abbreviations</p>
            </div>
        `;
    }

    if (modal) {
        modal.style.display = 'none';
    }
}

// Placeholder functions - these will be implemented in separate files
// due to their complexity and size

/**
 * Format scripture references according to the specified rules
 */
export function formatScriptureReferences(text) {
    console.log('Formatting scripture references...');
    console.log('Input text:', JSON.stringify(text));

    // Bible book abbreviations mapping to full names
    const bookAbbreviations = {
        // Old Testament
        'Gen.': 'Genesis', 'Gen': 'Genesis',
        'Ex.': 'Exodus', 'Exod.': 'Exodus', 'Exod': 'Exodus',
        'Lev.': 'Leviticus', 'Lev': 'Leviticus',
        'Num.': 'Numbers', 'Num': 'Numbers',
        'Deut.': 'Deuteronomy', 'Deut': 'Deuteronomy',
        'Josh.': 'Joshua', 'Josh': 'Joshua',
        'Judg.': 'Judges', 'Judg': 'Judges',
        'Ruth': 'Ruth',
        '1 Sam.': '1 Samuel', '1Sam.': '1 Samuel', '1Sam': '1 Samuel',
        '2 Sam.': '2 Samuel', '2Sam.': '2 Samuel', '2Sam': '2 Samuel',
        '1 Kings': '1 Kings', '1Kgs.': '1 Kings', '1Kgs': '1 Kings',
        '2 Kings': '2 Kings', '2Kgs.': '2 Kings', '2Kgs': '2 Kings',
        '1 Chr.': '1 Chronicles', '1Chr.': '1 Chronicles', '1Chr': '1 Chronicles',
        '2 Chr.': '2 Chronicles', '2Chr.': '2 Chronicles', '2Chr': '2 Chronicles',
        'Ezra': 'Ezra',
        'Neh.': 'Nehemiah', 'Neh': 'Nehemiah',
        'Esth.': 'Esther', 'Esth': 'Esther',
        'Job': 'Job',
        'Ps.': 'Psalms', 'Ps': 'Psalms', 'Psa.': 'Psalms', 'Psa': 'Psalms',
        'Prov.': 'Proverbs', 'Prov': 'Proverbs',
        'Eccl.': 'Ecclesiastes', 'Eccl': 'Ecclesiastes', 'Ecc.': 'Ecclesiastes', 'Ecc': 'Ecclesiastes',
        'Song': 'Song of Songs', 'SS': 'Song of Songs',
        'Is.': 'Isaiah', 'Isa.': 'Isaiah', 'Isa': 'Isaiah',
        'Jer.': 'Jeremiah', 'Jer': 'Jeremiah',
        'Lam.': 'Lamentations', 'Lam': 'Lamentations',
        'Ezek.': 'Ezekiel', 'Ezek': 'Ezekiel', 'Ez.': 'Ezekiel', 'Ez': 'Ezekiel',
        'Dan.': 'Daniel', 'Dan': 'Daniel',
        'Hos.': 'Hosea', 'Hos': 'Hosea',
        'Joel': 'Joel',
        'Amos': 'Amos',
        'Obad.': 'Obadiah', 'Obad': 'Obadiah',
        'Jon.': 'Jonah', 'Jon': 'Jonah',
        'Mic.': 'Micah', 'Mic': 'Micah',
        'Nah.': 'Nahum', 'Nah': 'Nahum',
        'Hab.': 'Habakkuk', 'Hab': 'Habakkuk',
        'Zeph.': 'Zephaniah', 'Zeph': 'Zephaniah',
        'Hag.': 'Haggai', 'Hag': 'Haggai',
        'Zech.': 'Zechariah', 'Zech': 'Zechariah',
        'Mal.': 'Malachi', 'Mal': 'Malachi',

        // New Testament
        'Mt.': 'Matthew', 'Matt.': 'Matthew', 'Matt': 'Matthew',
        'Mk.': 'Mark', 'Mark': 'Mark',
        'Lk.': 'Luke', 'Luke': 'Luke',
        'Jn.': 'John', 'John': 'John',
        'Acts': 'Acts',
        'Rom.': 'Romans', 'Rom': 'Romans',
        '1 Cor.': '1 Corinthians', '1Cor.': '1 Corinthians', '1Cor': '1 Corinthians',
        '2 Cor.': '2 Corinthians', '2Cor.': '2 Corinthians', '2Cor': '2 Corinthians',
        'Gal.': 'Galatians', 'Gal': 'Galatians',
        'Eph.': 'Ephesians', 'Eph': 'Ephesians',
        'Phil.': 'Philippians', 'Phil': 'Philippians',
        'Col.': 'Colossians', 'Col': 'Colossians',
        '1 Thess.': '1 Thessalonians', '1Thess.': '1 Thessalonians', '1Thess': '1 Thessalonians',
        '2 Thess.': '2 Thessalonians', '2Thess.': '2 Thessalonians', '2Thess': '2 Thessalonians',
        '1 Tim.': '1 Timothy', '1Tim.': '1 Timothy', '1Tim': '1 Timothy',
        '2 Tim.': '2 Timothy', '2Tim.': '2 Timothy', '2Tim': '2 Timothy',
        'Tit.': 'Titus', 'Tit': 'Titus',
        'Philem.': 'Philemon', 'Philem': 'Philemon',
        'Heb.': 'Hebrews', 'Heb': 'Hebrews',
        'Jas.': 'James', 'Jas': 'James',
        '1 Pet.': '1 Peter', '1Pet.': '1 Peter', '1Pet': '1 Peter',
        '2 Pet.': '2 Peter', '2Pet.': '2 Peter', '2Pet': '2 Peter',
        '1 Jn.': '1 John', '1John': '1 John',
        '2 Jn.': '2 John', '2John': '2 John',
        '3 Jn.': '3 John', '3John': '3 John',
        'Jude': 'Jude',
        'Rev.': 'Revelation', 'Rev': 'Revelation'
    };

    // Pattern to match quoted text followed by scripture reference
    // Matches various quote types: 'text' "text" 'text' "text" etc.
    const scripturePattern = /(['""''])((?:(?!\1).){8,}?)\1\s*\(([^)]+)\)/g;

    console.log('Testing pattern against text...');
    console.log('Pattern:', scripturePattern);

    let formattedText = text;
    let hasChanges = false;

    formattedText = formattedText.replace(scripturePattern, (match, quote, quotedText, reference) => {
        // Check if the quoted text has 8 or more words
        const wordCount = quotedText.trim().split(/\s+/).length;
        if (wordCount < 8) {
            return match; // Don't format if less than 8 words
        }

        // Expand book abbreviations in the reference
        let expandedReference = reference;
        for (const [abbrev, fullName] of Object.entries(bookAbbreviations)) {
            const regex = new RegExp(`\\b${abbrev.replace('.', '\\.')}\\b`, 'gi');
            expandedReference = expandedReference.replace(regex, fullName);
        }

        // Format chapter and verse numbers with "chapter" and "verse" words
        // Pattern: BookName number, number -> BookName, chapter number, verse number
        expandedReference = expandedReference.replace(/^([^0-9]+)\s*(\d+),?\s*(\d+)$/, '$1, chapter $2, verse $3');
        // Pattern: BookName number:number -> BookName, chapter number, verse number
        expandedReference = expandedReference.replace(/^([^0-9]+)\s*(\d+):(\d+)$/, '$1, chapter $2, verse $3');
        // Pattern: BookName number -> BookName, chapter number (for chapter-only references)
        expandedReference = expandedReference.replace(/^([^0-9]+)\s*(\d+)$/, '$1, chapter $2');

        hasChanges = true;

        // Format as: quote, [quoted text] end quote, ([expanded reference])
        return `<strong>quote, ${quotedText} end quote, (${expandedReference})</strong>`;
    });

    if (hasChanges) {
        console.log('Scripture references formatted successfully');
    } else {
        console.log('No scripture references found to format');
    }

    return formattedText;
}

/**
 * Format quotes from written works according to the specified rules
 */
export function formatQuotesFromWrittenWorks(text) {
    // This function will be moved to a separate quotes.js file
    // For now, return the original text
    console.log('Quote formatting not yet implemented in modular structure');
    return text;
}

/**
 * Expand all abbreviations in the text
 */
export function expandAllAbbreviations(text) {
    // This function will be moved to a separate abbreviations.js file
    // For now, return the original text
    console.log('Abbreviation expansion not yet implemented in modular structure');
    return text;
}

/**
 * Format papal names and saint abbreviations
 */
export function formatPapalNamesAndSaints(text) {
    // This function will be moved to a separate papal.js file
    // For now, return the original text
    console.log('Papal/saint formatting not yet implemented in modular structure');
    return text;
}

/**
 * Format footnotes for audiobook narration
 */
export function formatFootnotesForAudiobook(text) {
    // This function will be moved to a separate footnotes.js file
    // For now, return the original text
    console.log('Footnote formatting not yet implemented in modular structure');
    return text;
}
